import logging
import os
import argparse
from pathlib import Path
from PIL import Image
from contextlib import nullcontext

import numpy as np
import torch
from tqdm.auto import tqdm
from diffusers.utils import check_min_version

from pipeline import LotusGPipeline, LotusDPipeline
from utils.image_utils import colorize_depth_map
from utils.seed_all import seed_all

'''
【250910】MTL inference script for multi-task learning model
Supports simultaneous depth and normal estimation from single RGB input
Uses timestep-based task conditioning instead of task embeddings
'''

check_min_version('0.28.0.dev0')

def load_mtl_pipeline(model_path, mode, dtype, device):
    """
    Load MTL pipeline with proper handling of missing components
    """
    pipeline_class = LotusGPipeline if mode == 'generation' else LotusDPipeline

    try:
        # Try normal loading first
        pipeline = pipeline_class.from_pretrained(model_path, torch_dtype=dtype)
        logging.info("Successfully loaded pipeline with all components")
    except ValueError as e:
        if "expected" in str(e) and "but only" in str(e):
            # Handle missing components for MTL models
            logging.info("Loading MTL model with missing components...")
            pipeline = pipeline_class.from_pretrained(
                model_path,
                text_encoder=None,
                tokenizer=None,
                image_encoder=None,
                safety_checker=None,
                feature_extractor=None,
                torch_dtype=dtype,
            )
            logging.info("Successfully loaded MTL pipeline without missing components")
        else:
            raise e

    # Load empty text embeddings if available
    empty_text_embed_path = os.path.join(model_path, "empty_text_embeddings.pt")
    if os.path.exists(empty_text_embed_path):
        logging.info(f"Loading empty text embeddings from {empty_text_embed_path}")
        empty_text_embeddings = torch.load(empty_text_embed_path, map_location=device)
        # Cache the embeddings in the pipeline
        pipeline._cached_empty_text_embeddings = empty_text_embeddings.cpu()
        pipeline._cached_empty_text_embed_path = empty_text_embed_path
    else:
        logging.warning("No empty_text_embeddings.pt found. This may cause issues with MTL models.")

    return pipeline

def parse_args():
    '''Set the Args for MTL inference'''
    parser = argparse.ArgumentParser(
        description="Run Lotus MTL inference for simultaneous depth and normal estimation"
    )
    # model settings
    parser.add_argument(
        "--pretrained_model_name_or_path",
        type=str,
        default=None,
        help="pretrained MTL model path from hugging face or local dir",
    )
    parser.add_argument(
        "--prediction_type",
        type=str,
        default="sample",
        help="The used prediction_type. ",
    )
    # MTL-specific timestep arguments
    parser.add_argument(
        "--timestep_depth",
        type=int,
        default=995,
        help="Timestep for depth estimation task (default: 800)"
    )
    parser.add_argument(
        "--timestep_normal",
        type=int,
        default=999,
        help="Timestep for normal estimation task (default: 600)"
    )
    parser.add_argument(
        "--mode",
        type=str,
        default="regression", # "generation"
        help="Whether to use the generation or regression pipeline."
    )
    parser.add_argument(
        "--task_name",
        type=str,
        default="mtl", # "depth", "normal", "mtl"
        help="Task name: 'mtl' for both depth and normal, 'depth' for depth only, 'normal' for normal only"
    )
    parser.add_argument(
        "--disparity",
        action="store_true",
        help="Use disparity instead of depth for depth task"
    )
    parser.add_argument(
        "--enable_xformers_memory_efficient_attention", action="store_true", help="Whether or not to use xformers."
    )

    # inference settings
    parser.add_argument("--seed", type=int, default=None, help="Random seed.")
    parser.add_argument(
        "--output_dir", type=str, required=True, help="Output directory."
    )
    parser.add_argument(
        "--input_dir", type=str, required=True, help="Input directory."
    )
    parser.add_argument(
        "--half_precision",
        action="store_true",
        help="Run with half-precision (16-bit float), might lead to suboptimal result.",
    )
    parser.add_argument(
        "--processing_res",
        type=int,
        default=None,
        help="Maximum resolution of processing. 0 for using input image resolution. Default: 768.",
    )
    parser.add_argument(
        "--output_processing_res",
        action="store_true",
        help="When input is resized, out put depth at resized operating resolution. Default: False.",
    )
    parser.add_argument(
        "--resample_method",
        choices=["bilinear", "bicubic", "nearest"],
        default="bilinear",
        help="Resampling method used to resize images and depth predictions. This can be one of `bilinear`, `bicubic` or `nearest`. Default: `bilinear`",
    )

    args = parser.parse_args()

    return args


def main():
    logging.basicConfig(level=logging.INFO)
    logging.info(f"Run inference...")

    args = parse_args()

    # -------------------- Preparation --------------------
    # Random seed
    if args.seed is not None:
        seed_all(args.seed)

    # Output directories
    os.makedirs(args.output_dir, exist_ok=True)
    logging.info(f"Output dir = {args.output_dir}")

    # Create output directories based on task
    if args.task_name == "mtl":
        # For MTL, create separate directories for depth and normal
        output_dir_depth_color = os.path.join(args.output_dir, 'depth_vis')
        output_dir_depth_npy = os.path.join(args.output_dir, 'depth')
        output_dir_normal_color = os.path.join(args.output_dir, 'normal_vis')
        output_dir_normal_npy = os.path.join(args.output_dir, 'normal')

        os.makedirs(output_dir_depth_color, exist_ok=True)
        os.makedirs(output_dir_depth_npy, exist_ok=True)
        os.makedirs(output_dir_normal_color, exist_ok=True)
        os.makedirs(output_dir_normal_npy, exist_ok=True)
    else:
        # For single task, use original structure
        output_dir_color = os.path.join(args.output_dir, f'{args.task_name}_vis')
        output_dir_npy = os.path.join(args.output_dir, f'{args.task_name}')
        os.makedirs(output_dir_color, exist_ok=True)
        os.makedirs(output_dir_npy, exist_ok=True)

    # half_precision
    if args.half_precision:
        dtype = torch.float16
        logging.info(f"Running with half precision ({dtype}).")
    else:
        dtype = torch.float32
    
    # processing_res
    processing_res = args.processing_res
    match_input_res = not args.output_processing_res
    if 0 == processing_res and match_input_res is False:
        logging.warning(
            "Processing at native resolution without resizing output might NOT lead to exactly the same resolution, due to the padding and pooling properties of conv layers."
        )
    resample_method = args.resample_method

    # -------------------- Device --------------------
    if torch.cuda.is_available():
        device = torch.device("cuda")
    else:
        device = torch.device("cpu")
        logging.warning("CUDA is not available. Running on CPU will be slow.")
    logging.info(f"Device = {device}")

    # -------------------- Data --------------------
    root_dir = Path(args.input_dir)
    test_images = list(root_dir.rglob('*.png')) + list(root_dir.rglob('*.jpg'))
    test_images = sorted(test_images)
    print('==> There are', len(test_images), 'images for validation.')
    # -------------------- Model --------------------

    # Load MTL pipeline with proper error handling
    pipeline = load_mtl_pipeline(
        args.pretrained_model_name_or_path,
        args.mode,
        dtype,
        device
    )

    logging.info(f"Successfully loaded pipeline from {args.pretrained_model_name_or_path}")
    logging.info(f"processing_res = {processing_res or pipeline.default_processing_resolution}")

    pipeline = pipeline.to(device)
    pipeline.set_progress_bar_config(disable=True)

    if args.enable_xformers_memory_efficient_attention:
        pipeline.enable_xformers_memory_efficient_attention()

    if args.seed is None:
        generator = None
    else:
        generator = torch.Generator(device=device).manual_seed(args.seed)

    # -------------------- MTL Inference and saving --------------------
    with torch.no_grad():
        for i in tqdm(range(len(test_images))):
            if torch.backends.mps.is_available():
                autocast_ctx = nullcontext()
            else:
                autocast_ctx = torch.autocast(pipeline.device.type)
            with autocast_ctx:
                # Preprocess validation image
                test_image = Image.open(test_images[i]).convert('RGB')
                test_image = np.array(test_image).astype(np.float32)
                test_image = torch.tensor(test_image).permute(2,0,1).unsqueeze(0)
                test_image = test_image / 127.5 - 1.0
                test_image = test_image.to(device)

                save_file_name = os.path.basename(test_images[i])[:-4]

                if args.task_name == "mtl":
                    # MTL: Run both depth and normal estimation

                    # Depth estimation using depth timestep
                    pred_depth = pipeline(
                        rgb_in=test_image,
                        prompt='',
                        num_inference_steps=1,
                        generator=generator,
                        output_type='np',
                        timesteps=[args.timestep_depth],  # Use depth-specific timestep
                        processing_res=processing_res,
                        match_input_res=match_input_res,
                        resample_method=resample_method,
                    ).images[0]

                    # Normal estimation using normal timestep
                    pred_normal = pipeline(
                        rgb_in=test_image,
                        prompt='',
                        num_inference_steps=1,
                        generator=generator,
                        output_type='np',
                        timesteps=[args.timestep_normal],  # Use normal-specific timestep
                        processing_res=processing_res,
                        match_input_res=match_input_res,
                        resample_method=resample_method,
                    ).images[0]

                    # Post-process depth prediction
                    depth_npy = pred_depth.mean(axis=-1)
                    depth_color = colorize_depth_map(depth_npy, reverse_color=args.disparity)

                    # Post-process normal prediction
                    normal_npy = pred_normal
                    normal_color = Image.fromarray((normal_npy * 255).astype(np.uint8))

                    # Save depth results
                    depth_color.save(os.path.join(output_dir_depth_color, f'{save_file_name}.png'))
                    np.save(os.path.join(output_dir_depth_npy, f'{save_file_name}.npy'), depth_npy)

                    # Save normal results
                    normal_color.save(os.path.join(output_dir_normal_color, f'{save_file_name}.png'))
                    np.save(os.path.join(output_dir_normal_npy, f'{save_file_name}.npy'), normal_npy)

                elif args.task_name == "depth":
                    # Single depth estimation
                    pred = pipeline(
                        rgb_in=test_image,
                        prompt='',
                        num_inference_steps=1,
                        generator=generator,
                        output_type='np',
                        timesteps=[args.timestep_depth],
                        processing_res=processing_res,
                        match_input_res=match_input_res,
                        resample_method=resample_method,
                    ).images[0]

                    output_npy = pred.mean(axis=-1)
                    output_color = colorize_depth_map(output_npy, reverse_color=args.disparity)

                    output_color.save(os.path.join(output_dir_color, f'{save_file_name}.png'))
                    np.save(os.path.join(output_dir_npy, f'{save_file_name}.npy'), output_npy)

                elif args.task_name == "normal":
                    # Single normal estimation
                    pred = pipeline(
                        rgb_in=test_image,
                        prompt='',
                        num_inference_steps=1,
                        generator=generator,
                        output_type='np',
                        timesteps=[args.timestep_normal],
                        processing_res=processing_res,
                        match_input_res=match_input_res,
                        resample_method=resample_method,
                    ).images[0]

                    output_npy = pred
                    output_color = Image.fromarray((output_npy * 255).astype(np.uint8))

                    output_color.save(os.path.join(output_dir_color, f'{save_file_name}.png'))
                    np.save(os.path.join(output_dir_npy, f'{save_file_name}.npy'), output_npy)

                else:
                    raise ValueError(f"Unsupported task_name: {args.task_name}. Use 'mtl', 'depth', or 'normal'.")

            torch.cuda.empty_cache()

    # Print completion message with task-specific information
    if args.task_name == "mtl":
        print('==> MTL Inference is done.')
        print(f'==> Depth results saved to: {output_dir_depth_color} (visualizations) and {output_dir_depth_npy} (numpy arrays)')
        print(f'==> Normal results saved to: {output_dir_normal_color} (visualizations) and {output_dir_normal_npy} (numpy arrays)')
    else:
        print(f'==> {args.task_name.capitalize()} inference is done.')
        print(f'==> Results saved to: {args.output_dir}')


if __name__ == '__main__':
    main()
