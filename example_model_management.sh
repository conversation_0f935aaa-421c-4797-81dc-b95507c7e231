#!/bin/bash

# MTL 模型管理示例脚本
# 演示如何使用 manage_mtl_model.py 工具

# 设置模型路径（请根据实际情况修改）
MODEL_PATH="outputs/lotus_mtl_training"  # 替换为你的实际模型路径

echo "🚀 MTL 模型管理示例"
echo "模型路径: $MODEL_PATH"
echo "=" * 60

# 检查模型路径是否存在
if [ ! -d "$MODEL_PATH" ]; then
    echo "❌ 模型路径不存在: $MODEL_PATH"
    echo "请修改 MODEL_PATH 变量为你的实际模型路径"
    exit 1
fi

echo "1️⃣  分析模型目录结构和文件大小"
echo "-----------------------------------"
python manage_mtl_model.py "$MODEL_PATH" --analyze

echo ""
echo "2️⃣  验证推理模型完整性"
echo "-----------------------------------"
python manage_mtl_model.py "$MODEL_PATH" --validate

echo ""
echo "3️⃣  创建轻量级推理模型包"
echo "-----------------------------------"
INFERENCE_PACKAGE_PATH="${MODEL_PATH}_inference_only"
python manage_mtl_model.py "$MODEL_PATH" --create-package "$INFERENCE_PACKAGE_PATH"

echo ""
echo "4️⃣  清理检查点文件（保留最新）"
echo "-----------------------------------"
echo "⚠️  这将删除除最新检查点外的所有检查点文件"
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    python manage_mtl_model.py "$MODEL_PATH" --clean --keep-latest
else
    echo "跳过清理操作"
fi

echo ""
echo "5️⃣  完全清理所有检查点文件"
echo "-----------------------------------"
echo "⚠️  这将删除所有检查点文件，无法恢复训练"
read -p "是否继续？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    python manage_mtl_model.py "$MODEL_PATH" --clean
else
    echo "跳过完全清理操作"
fi

echo ""
echo "✅ 模型管理完成！"
echo ""
echo "📋 使用建议:"
echo "- 推理使用: $MODEL_PATH"
echo "- 轻量级推理包: $INFERENCE_PACKAGE_PATH"
echo "- 继续训练: 使用 --resume_from_checkpoint 参数"

echo ""
echo "🔧 常用命令:"
echo "# 分析模型"
echo "python manage_mtl_model.py $MODEL_PATH --analyze"
echo ""
echo "# 验证模型"
echo "python manage_mtl_model.py $MODEL_PATH --validate"
echo ""
echo "# 清理检查点"
echo "python manage_mtl_model.py $MODEL_PATH --clean --keep-latest"
echo ""
echo "# 创建推理包"
echo "python manage_mtl_model.py $MODEL_PATH --create-package /path/to/inference/package"
