#!/usr/bin/env python3
"""
Test script for MTL model loading
测试 MTL 模型加载是否正常工作
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add current directory to path to import local modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pipeline import LotusGPipeline, LotusDPipeline

def test_mtl_model_loading(model_path, mode="regression"):
    """
    Test MTL model loading with different scenarios
    """
    print(f"🧪 Testing MTL model loading from: {model_path}")
    print(f"Mode: {mode}")
    print("=" * 60)
    
    if not os.path.exists(model_path):
        print(f"❌ Model path does not exist: {model_path}")
        return False
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Check device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    dtype = torch.float16 if torch.cuda.is_available() else torch.float32
    
    print(f"Device: {device}")
    print(f"Data type: {dtype}")
    print()
    
    try:
        # Test 1: Try normal loading
        print("🔄 Test 1: Normal loading...")
        pipeline_class = LotusGPipeline if mode == 'generation' else LotusDPipeline
        
        try:
            pipeline = pipeline_class.from_pretrained(model_path, torch_dtype=dtype)
            print("✅ Normal loading successful")
        except ValueError as e:
            if "expected" in str(e) and "but only" in str(e):
                print("⚠️  Normal loading failed (missing components), trying MTL loading...")
                
                # Test 2: MTL loading with missing components
                print("🔄 Test 2: MTL loading with missing components...")
                pipeline = pipeline_class.from_pretrained(
                    model_path,
                    text_encoder=None,
                    tokenizer=None,
                    image_encoder=None,
                    safety_checker=None,
                    feature_extractor=None,
                    torch_dtype=dtype,
                )
                print("✅ MTL loading successful")
            else:
                print(f"❌ Loading failed with error: {e}")
                return False
        
        # Test 3: Check empty text embeddings
        print("🔄 Test 3: Checking empty text embeddings...")
        empty_text_embed_path = os.path.join(model_path, "empty_text_embeddings.pt")
        if os.path.exists(empty_text_embed_path):
            try:
                empty_text_embeddings = torch.load(empty_text_embed_path, map_location=device)
                print(f"✅ Empty text embeddings loaded: shape {empty_text_embeddings.shape}")
                
                # Cache in pipeline
                pipeline._cached_empty_text_embeddings = empty_text_embeddings.cpu()
                pipeline._cached_empty_text_embed_path = empty_text_embed_path
            except Exception as e:
                print(f"⚠️  Failed to load empty text embeddings: {e}")
        else:
            print("⚠️  No empty_text_embeddings.pt found")
        
        # Test 4: Move to device
        print("🔄 Test 4: Moving pipeline to device...")
        pipeline = pipeline.to(device)
        print("✅ Pipeline moved to device successfully")
        
        # Test 5: Check pipeline components
        print("🔄 Test 5: Checking pipeline components...")
        components = {
            'unet': pipeline.unet,
            'vae': pipeline.vae,
            'scheduler': pipeline.scheduler,
            'text_encoder': getattr(pipeline, 'text_encoder', None),
            'tokenizer': getattr(pipeline, 'tokenizer', None),
        }
        
        for name, component in components.items():
            if component is not None:
                print(f"  ✅ {name}: {type(component).__name__}")
            else:
                print(f"  ❌ {name}: None (expected for MTL models)")
        
        # Test 6: Check UNet configuration
        print("🔄 Test 6: Checking UNet configuration...")
        unet_config = pipeline.unet.config
        in_channels = getattr(unet_config, 'in_channels', 'unknown')
        print(f"  UNet in_channels: {in_channels}")
        
        if in_channels == 8:
            print("  ✅ UNet has 8 input channels (correct for MTL)")
        elif in_channels == 4:
            print("  ⚠️  UNet has 4 input channels (may not be MTL model)")
        else:
            print(f"  ❓ UNet has {in_channels} input channels")
        
        print("\n🎉 All tests passed! MTL model loading is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    import argparse
    parser = argparse.ArgumentParser(description="Test MTL model loading")
    parser.add_argument("model_path", help="Path to MTL model directory")
    parser.add_argument("--mode", choices=["generation", "regression"], default="regression", 
                       help="Pipeline mode to test")
    
    args = parser.parse_args()
    
    success = test_mtl_model_loading(args.model_path, args.mode)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
