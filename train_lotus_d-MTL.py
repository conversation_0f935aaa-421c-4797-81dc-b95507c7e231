#!/usr/bin/env python
# coding=utf-8
# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import argparse
import logging
import math
import os
import random
import shutil
from contextlib import nullcontext
from pathlib import Path
from PIL import Image
from glob import glob
from easydict import EasyDict

import accelerate
import datasets
import numpy as np
import torch
import torch.nn.functional as F
import torch.utils.checkpoint
import torch.nn as nn
import transformers
from accelerate import Accelerator
from accelerate.logging import get_logger
from accelerate.state import AcceleratorState
from accelerate.utils import ProjectConfiguration, set_seed
from huggingface_hub import create_repo
from packaging import version
from tqdm.auto import tqdm
from transformers import CLIPTextModel, CLIPTokenizer
from transformers.utils import ContextManagers
from datetime import timedelta
from accelerate.utils import InitProcessGroupKwargs

import diffusers
from diffusers import AutoencoderKL, UNet2DConditionModel
from diffusers.optimization import get_scheduler
from diffusers.utils import check_min_version, deprecate
from diffusers.utils.import_utils import is_xformers_available
from diffusers.utils.torch_utils import is_compiled_module
from diffusers.training_utils import EMAModel
from huggingface_hub import upload_folder

from pipeline import LotusDPipeline
from utils.image_utils import concatenate_images, colorize_depth_map
from utils.hypersim_dataset import get_hypersim_dataset_depth_normal
from utils.vkitti_dataset import VKITTIDataset, VKITTITransform, collate_fn_vkitti

from eval import evaluation_depth, evaluation_normal

import tensorboard

import warnings
warnings.simplefilter(action='ignore', category=FutureWarning)
'''
Deterministic Multi-Task Learning (MTL) training script for Lotus-D
Based on train_lotus_g-MTL.py but adapted for deterministic paradigm:
- Removes noise addition during training (no noise_scheduler.add_noise())
- Uses clean RGB latents as UNet input (not concatenated with noisy target latents)
- Uses LotusDPipeline instead of LotusGPipeline
- Maintains timestep-based task conditioning system
- Preserves weighted task sampling and multi-task loss computation
'''
# Will error if the minimal version of diffusers is not installed. Remove at your own risks.
check_min_version("0.28.0.dev0")

logger = get_logger(__name__, log_level="INFO")

TOP5_STEPS_DEPTH = []
TOP5_STEPS_NORMAL = []

# MTL: Global timestep frequency counters
TIMESTEP_FREQUENCY = {
    'rgb': 0,
    'depth': 0,
    'normal': 0
}

def assign_timesteps_and_targets(batch, batch_size, args, device):
    """
    Dynamically assign timesteps and corresponding targets for each sample in the batch.
    Returns timesteps, target_images, and task_types for multi-task learning.
    Also updates global timestep frequency counters.

    Uses weighted random selection based on task ratios if specified.
    """
    global TIMESTEP_FREQUENCY

    timesteps = []
    target_images = []
    task_types = []

    # Define task choices and their corresponding timesteps
    task_choices = ['rgb', 'depth', 'normal']
    timestep_mapping = {
        'rgb': args.timestep_rgb,
        'depth': args.timestep_depth,
        'normal': args.timestep_normal
    }

    # Get task ratios (default to 1.0 for backward compatibility)
    task_ratios = {
        'rgb': getattr(args, 'task_ratio_rgb', 1.0),
        'depth': getattr(args, 'task_ratio_depth', 1.0),
        'normal': getattr(args, 'task_ratio_normal', 1.0)
    }

    # Create weighted task list for random selection
    weighted_tasks = []
    for task in task_choices:
        # Convert ratio to integer weight (multiply by 100 for precision)
        weight = max(1, int(task_ratios[task] * 100))
        weighted_tasks.extend([task] * weight)

    for i in range(batch_size):
        # Weighted random assignment based on task ratios
        task_choice = random.choice(weighted_tasks)

        # Update frequency counter
        TIMESTEP_FREQUENCY[task_choice] += 1

        timesteps.append(timestep_mapping[task_choice])
        task_types.append(task_choice)

        # Select corresponding target based on task
        if task_choice == 'rgb':
            target_images.append(batch["pixel_values"][i])
        elif task_choice == 'depth':
            target_images.append(batch["depth_values"][i])
        else:  # normal
            target_images.append(batch["normal_values"][i])

    return torch.tensor(timesteps, device=device).long(), torch.stack(target_images), task_types

def compute_multi_task_loss(model_pred, targets, task_types, valid_masks, args):
    """
    Compute task-specific losses based on assigned timesteps and task types.
    Applies configurable loss weights to each task's contribution to total loss.
    Returns total_loss and individual task losses for logging.
    """
    rgb_loss = torch.tensor(0.0, device=model_pred.device)
    depth_loss = torch.tensor(0.0, device=model_pred.device)
    normal_loss = torch.tensor(0.0, device=model_pred.device)

    rgb_count = depth_count = normal_count = 0

    for i, task_type in enumerate(task_types):
        pred_i = model_pred[i:i+1]
        target_i = targets[i:i+1]
        mask_i = valid_masks[i:i+1] if valid_masks is not None else torch.ones_like(pred_i).bool()

        if task_type == 'rgb':
            rgb_loss += F.mse_loss(pred_i[mask_i].float(), target_i[mask_i].float(), reduction="mean")
            rgb_count += 1
        elif task_type == 'depth':
            depth_loss += F.mse_loss(pred_i[mask_i].float(), target_i[mask_i].float(), reduction="mean")
            depth_count += 1
        else:  # normal
            normal_loss += F.mse_loss(pred_i[mask_i].float(), target_i[mask_i].float(), reduction="mean")
            normal_count += 1

    # Average losses by task count to normalize
    if rgb_count > 0:
        rgb_loss = rgb_loss / rgb_count
    if depth_count > 0:
        depth_loss = depth_loss / depth_count
    if normal_count > 0:
        normal_loss = normal_loss / normal_count

    # Get loss weights (default to 1.0 for backward compatibility)
    loss_weights = {
        'rgb': getattr(args, 'loss_weight_rgb', 1.0),
        'depth': getattr(args, 'loss_weight_depth', 1.0),
        'normal': getattr(args, 'loss_weight_normal', 1.0)
    }

    # Apply weights to compute total loss
    weighted_rgb_loss = rgb_loss * loss_weights['rgb']
    weighted_depth_loss = depth_loss * loss_weights['depth']
    weighted_normal_loss = normal_loss * loss_weights['normal']

    total_loss = weighted_rgb_loss + weighted_depth_loss + weighted_normal_loss

    # Return both unweighted losses (for monitoring) and weighted total loss
    return total_loss, rgb_loss, depth_loss, normal_loss, weighted_rgb_loss, weighted_depth_loss, weighted_normal_loss

def print_timestep_statistics(global_step):
    """
    Print timestep selection frequency statistics for monitoring task balance.
    """
    global TIMESTEP_FREQUENCY
    total_samples = sum(TIMESTEP_FREQUENCY.values())
    
    if total_samples > 0:
        rgb_freq = TIMESTEP_FREQUENCY['rgb'] / total_samples * 100
        depth_freq = TIMESTEP_FREQUENCY['depth'] / total_samples * 100
        normal_freq = TIMESTEP_FREQUENCY['normal'] / total_samples * 100
        
        logger.info(f"Step {global_step} - Timestep Frequency: RGB={rgb_freq:.1f}%, Depth={depth_freq:.1f}%, Normal={normal_freq:.1f}%")
        logger.info(f"Step {global_step} - Total samples processed: {total_samples}")

def run_example_validation(pipeline, task, args, step, accelerator, generator):
    validation_images = glob(os.path.join(args.validation_images, "*.jpg")) + glob(os.path.join(args.validation_images, "*.png"))
    validation_images = sorted(validation_images)
    print(validation_images)
    
    pred_annos = []
    input_images = []
    
    if task == "depth":
        for i in range(len(validation_images)):
            if torch.backends.mps.is_available():
                autocast_ctx = nullcontext()
            else:
                autocast_ctx = torch.autocast(accelerator.device.type)

            with autocast_ctx:
                # Preprocess validation image
                validation_image = Image.open(validation_images[i]).convert("RGB")
                input_images.append(validation_image)
                validation_image = np.array(validation_image).astype(np.float32)
                validation_image = torch.tensor(validation_image).permute(2,0,1).unsqueeze(0)
                validation_image = validation_image / 127.5 - 1.0 
                validation_image = validation_image.to(accelerator.device)

                task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(accelerator.device)
                task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)

                # Run - Deterministic single-step inference
                pred_depth = pipeline(
                    rgb_in=validation_image, 
                    task_emb=task_emb,
                    prompt="", 
                    timesteps=[args.timestep_depth],  # Use depth-specific timestep
                    output_type='np',
                    generator=generator, 
                    ).images[0]
                pred_depth = pred_depth.mean(axis=-1) # [0,1]
                pred_annos.append(pred_depth)

        # Save validation results
        concatenate_images(input_images, pred_annos, os.path.join(args.output_dir, f"validation_depth_{step}.png"))

    elif task == "normal":
        for i in range(len(validation_images)):
            if torch.backends.mps.is_available():
                autocast_ctx = nullcontext()
            else:
                autocast_ctx = torch.autocast(accelerator.device.type)

            with autocast_ctx:
                # Preprocess validation image
                validation_image = Image.open(validation_images[i]).convert("RGB")
                input_images.append(validation_image)
                validation_image = np.array(validation_image).astype(np.float32)
                validation_image = torch.tensor(validation_image).permute(2,0,1).unsqueeze(0)
                validation_image = validation_image / 127.5 - 1.0
                validation_image = validation_image.to(accelerator.device)

                task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(accelerator.device)
                task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)

                # Run - Deterministic single-step inference
                pred_normal = pipeline(
                    rgb_in=validation_image,
                    task_emb=task_emb,
                    prompt="",
                    timesteps=[args.timestep_normal],  # Use normal-specific timestep
                    generator=generator,
                    ).images[0]

                pred_annos.append(pred_normal)

        # Save validation results
        concatenate_images(input_images, pred_annos, os.path.join(args.output_dir, f"validation_normal_{step}.png"))

def run_evaluation(pipeline, task, args, step, accelerator):
    # Define prediction functions
    def gen_depth(rgb_in, pipe, prompt="", num_inference_steps=1):
        if torch.backends.mps.is_available():
                autocast_ctx = nullcontext()
        else:
            autocast_ctx = torch.autocast(pipe.device.type)

        with autocast_ctx:
            rgb_input = rgb_in / 255.0 * 2.0 - 1.0  #  [0, 255] -> [-1, 1]
            task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(pipe.device)
            task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)
            pred_depth = pipe(
                            rgb_in=rgb_input,
                            task_emb=task_emb,
                            prompt=prompt,
                            timesteps=[args.timestep_depth],  # Use depth-specific timestep
                            output_type='np',
                            ).images[0]
            pred_depth = pred_depth.mean(axis=-1) # [0,1]
        return pred_depth

    def gen_normal(img, pipe, prompt="", num_inference_steps=1):
        if torch.backends.mps.is_available():
                autocast_ctx = nullcontext()
        else:
            autocast_ctx = torch.autocast(pipe.device.type)

        with autocast_ctx:
            img = img / 255.0 * 2.0 - 1.0  #  [0, 255] -> [-1, 1]
            task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(pipe.device)
            task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)
            pred_normal = pipe(
                            rgb_in=img, # [-1,1]
                            task_emb=task_emb,
                            prompt=prompt,
                            timesteps=[args.timestep_normal],  # Use normal-specific timestep
                            output_type='pt',
                            ).images[0] # [0,1], (3,h,w)
            pred_normal = (pred_normal*2-1.0).unsqueeze(0) # [-1,1], (1,3,h,w)
        return pred_normal

    # Run evaluation based on task
    if task == "depth":
        evaluation_depth(gen_depth, pipeline, args.base_test_data_dir, step, args.output_dir)
    elif task == "normal":
        evaluation_normal(gen_normal, pipeline, args.base_test_data_dir, step, args.output_dir)

def log_validation(
    vae,
    text_encoder,
    tokenizer,
    unet,
    args,
    accelerator,
    weight_dtype,
    step,
):
    logger.info("Running validation... ")

    task = args.task_name

    # Load pipeline for validation
    pipeline = LotusDPipeline.from_pretrained(
        args.pretrained_model_name_or_path,
        vae=accelerator.unwrap_model(vae),
        text_encoder=accelerator.unwrap_model(text_encoder),
        tokenizer=tokenizer,
        unet=accelerator.unwrap_model(unet),
        revision=args.revision,
        variant=args.variant,
        torch_dtype=weight_dtype,
    )
    pipeline = pipeline.to(accelerator.device)
    pipeline.set_progress_bar_config(disable=True)

    if args.enable_xformers_memory_efficient_attention:
        pipeline.enable_xformers_memory_efficient_attention()

    if args.seed is None:
        generator = None
    else:
        generator = torch.Generator(device=accelerator.device).manual_seed(args.seed)

    # Run example-validation
    run_example_validation(pipeline, task, args, step, accelerator, generator)

    # Run evaluation
    run_evaluation(pipeline, task, args, step, accelerator)

    del pipeline
    torch.cuda.empty_cache()

def parse_args():
    parser = argparse.ArgumentParser(description="Simple example of a training script.")
    parser.add_argument(
        "--pretrained_model_name_or_path",
        type=str,
        default=None,
        required=True,
        help="Path to pretrained model or model identifier from huggingface.co/models.",
    )
    parser.add_argument(
        "--revision",
        type=str,
        default=None,
        required=False,
        help="Revision of pretrained model identifier from huggingface.co/models.",
    )
    parser.add_argument(
        "--variant",
        type=str,
        default=None,
        help="Variant of the model files of the pretrained model identifier from huggingface.co/models, 'e.g.' fp16",
    )
    parser.add_argument(
        "--train_data_dir_hypersim",
        type=str,
        default=None,
        help=(
            "A folder containing the training data of hypersim. "
        ),
    )
    parser.add_argument(
        "--train_data_dir_vkitti",
        type=str,
        default=None,
        help=(
            "A folder containing the training data of vkitti. "
        ),
    )
    parser.add_argument(
        "--max_train_samples",
        type=int,
        default=None,
        help=(
            "For debugging purposes or quicker training, truncate the number of training examples to this "
            "value if set."
        ),
    )
    # MTL: Task-specific timestep arguments
    parser.add_argument(
        "--timestep_rgb",
        type=int,
        default=999,
        help="The timestep for RGB reconstruction task. "
    )
    parser.add_argument(
        "--timestep_depth",
        type=int,
        default=800,
        help="The timestep for depth estimation task. "
    )
    parser.add_argument(
        "--timestep_normal",
        type=int,
        default=600,
        help="The timestep for normal estimation task. "
    )
    parser.add_argument(
        "--base_test_data_dir",
        type=str,
        default="datasets/eval/",
        help="The base directory for the test data. "
    )
    parser.add_argument(
        "--task_name",
        type=str,
        default="mtl",
        help="The task identifier for multi-task learning. "
    )
    # MTL: Task frequency ratio arguments
    parser.add_argument(
        "--task_ratio_rgb",
        type=float,
        default=1.0,
        help="Frequency ratio for RGB reconstruction task (default: 1.0)"
    )
    parser.add_argument(
        "--task_ratio_depth",
        type=float,
        default=1.0,
        help="Frequency ratio for depth estimation task (default: 1.0)"
    )
    parser.add_argument(
        "--task_ratio_normal",
        type=float,
        default=1.0,
        help="Frequency ratio for normal estimation task (default: 1.0)"
    )
    # MTL: Loss weight arguments
    parser.add_argument(
        "--loss_weight_rgb",
        type=float,
        default=1.0,
        help="Loss weight for RGB reconstruction task (default: 1.0)"
    )
    parser.add_argument(
        "--loss_weight_depth",
        type=float,
        default=1.0,
        help="Loss weight for depth estimation task (default: 1.0)"
    )
    parser.add_argument(
        "--loss_weight_normal",
        type=float,
        default=1.0,
        help="Loss weight for normal estimation task (default: 1.0)"
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        default="lotus-d-mtl-model",
        help="The output directory where the model predictions and checkpoints will be written.",
    )
    parser.add_argument(
        "--cache_dir",
        type=str,
        default=None,
        help="The directory where the downloaded models and datasets will be stored.",
    )
    parser.add_argument(
        "--seed", type=int, default=None, help="A seed for reproducible training."
    )
    parser.add_argument(
        "--resolution_hypersim",
        type=int,
        default=512,
        help=(
            "The resolution for input images for hypersim, all the images in the train/validation dataset will be resized to this"
            " resolution"
        ),
    )
    parser.add_argument(
        "--resolution_vkitti",
        type=int,
        default=512,
        help=(
            "The resolution for input images for vkitti, all the images in the train/validation dataset will be resized to this"
            " resolution"
        ),
    )
    parser.add_argument(
        "--train_batch_size", type=int, default=16, help="Batch size (per device) for the training dataloader."
    )
    parser.add_argument("--num_train_epochs", type=int, default=100)
    parser.add_argument(
        "--max_train_steps",
        type=int,
        default=None,
        help="Total number of training steps to perform.  If provided, overrides num_train_epochs.",
    )
    parser.add_argument(
        "--gradient_accumulation_steps",
        type=int,
        default=1,
        help="Number of updates steps to accumulate before performing a backward/update pass.",
    )
    parser.add_argument(
        "--gradient_checkpointing",
        action="store_true",
        help="Whether or not to use gradient checkpointing to save memory at the expense of slower backward pass.",
    )
    parser.add_argument(
        "--learning_rate",
        type=float,
        default=1e-4,
        help="Initial learning rate (after the potential warmup period) to use.",
    )
    parser.add_argument(
        "--scale_lr",
        action="store_true",
        default=False,
        help="Scale the learning rate by the number of GPUs, gradient accumulation steps, and batch size.",
    )
    parser.add_argument(
        "--lr_scheduler",
        type=str,
        default="constant",
        help=(
            'The scheduler type to use. Choose between ["linear", "cosine", "cosine_with_restarts", "polynomial",'
            ' "constant", "constant_with_warmup"]'
        ),
    )
    parser.add_argument(
        "--lr_warmup_steps", type=int, default=500, help="Number of steps for the warmup in the lr scheduler."
    )
    parser.add_argument(
        "--use_8bit_adam", action="store_true", help="Whether or not to use 8-bit Adam from bitsandbytes."
    )
    parser.add_argument(
        "--allow_tf32",
        action="store_true",
        help=(
            "Whether or not to allow TF32 on Ampere GPUs. Can be used to speed up training. For more information, see"
            " https://pytorch.org/docs/stable/notes/cuda.html#tensorfloat-32-tf32-on-ampere-devices"
        ),
    )
    parser.add_argument(
        "--dataloader_num_workers",
        type=int,
        default=0,
        help=(
            "Number of subprocesses to use for data loading. 0 means that the data will be loaded in the main process."
        ),
    )
    parser.add_argument("--adam_beta1", type=float, default=0.9, help="The beta1 parameter for the Adam optimizer.")
    parser.add_argument("--adam_beta2", type=float, default=0.999, help="The beta2 parameter for the Adam optimizer.")
    parser.add_argument("--adam_weight_decay", type=float, default=1e-2, help="Weight decay to use.")
    parser.add_argument("--adam_epsilon", type=float, default=1e-08, help="Epsilon value for the Adam optimizer")
    parser.add_argument("--max_grad_norm", default=1.0, type=float, help="Max gradient norm.")
    parser.add_argument("--push_to_hub", action="store_true", help="Whether or not to push the model to the Hub.")
    parser.add_argument("--hub_token", type=str, default=None, help="The token to use to push to the Model Hub.")
    parser.add_argument(
        "--prediction_type",
        type=str,
        default=None,
        help="The prediction_type that shall be used for training. Choose between 'epsilon' or 'v_prediction' or leave `None`. If left to `None` the default prediction type of the scheduler: `noise_scheduler.config.prediction_type` is chosen.",
    )
    parser.add_argument(
        "--hub_model_id",
        type=str,
        default=None,
        help="The name of the repository to keep in sync with the local `output_dir`.",
    )
    parser.add_argument(
        "--logging_dir",
        type=str,
        default="logs",
        help=(
            "[TensorBoard](https://www.tensorflow.org/tensorboard) log directory. Will default to"
            " *output_dir/runs/**CURRENT_DATETIME_HOSTNAME***."
        ),
    )
    parser.add_argument(
        "--mixed_precision",
        type=str,
        default=None,
        choices=["no", "fp16", "bf16"],
        help=(
            "Whether to use mixed precision. Choose between fp16 and bf16 (bfloat16). Bf16 requires PyTorch >="
            " 1.10.and an Nvidia Ampere GPU.  Default to the value of accelerate config of the current system or the"
            " flag passed with the `accelerate.launch` command. Use this argument to override the accelerate config."
        ),
    )
    parser.add_argument(
        "--report_to",
        type=str,
        default="tensorboard",
        help=(
            'The integration to report the results and logs to. Supported platforms are `"tensorboard"`'
            ' (default), `"wandb"` and `"comet_ml"`. Use `"all"` to report to all integrations.'
        ),
    )
    parser.add_argument("--local_rank", type=int, default=-1, help="For distributed training: local_rank")
    parser.add_argument(
        "--checkpointing_steps",
        type=int,
        default=500,
        help=(
            "Save a checkpoint of the training state every X updates. These checkpoints are only suitable for resuming"
            " training using `--resume_from_checkpoint`."
        ),
    )
    parser.add_argument(
        "--checkpoints_total_limit",
        type=int,
        default=None,
        help=("Max number of checkpoints to store."),
    )
    parser.add_argument(
        "--resume_from_checkpoint",
        type=str,
        default=None,
        help=(
            "Whether training should be resumed from a previous checkpoint. Use a path saved by"
            ' `--checkpointing_steps`, or `"latest"` to automatically select the last available checkpoint.'
        ),
    )
    parser.add_argument(
        "--enable_xformers_memory_efficient_attention", action="store_true", help="Whether or not to use xformers."
    )
    parser.add_argument("--FULL_EVALUATION", action="store_true")
    parser.add_argument("--save_pred_vis", action="store_true")
    parser.add_argument(
        "--validation_images",
        type=str,
        default=None,
        help="A folder containing the validation images.",
    )
    parser.add_argument(
        "--validation_steps",
        type=int,
        default=100,
        help="Run validation every X steps.",
    )
    parser.add_argument(
        "--tracker_project_name",
        type=str,
        default="train_lotus_d_mtl",
        help=(
            "The `project_name` argument passed to Accelerator.init_trackers for"
            " more information see https://huggingface.co/docs/accelerate/v0.17.0/en/package_reference/accelerator#accelerate.Accelerator"
        ),
    )
    # Dataset-specific arguments
    parser.add_argument("--random_flip", action="store_true", help="Whether to randomly flip images horizontally")
    parser.add_argument("--mix_dataset", action="store_true", help="Whether to mix hypersim and vkitti datasets")
    parser.add_argument("--prob_hypersim", type=float, default=0.9, help="Probability of using hypersim vs vkitti")
    parser.add_argument("--norm_type", type=str, default="trunc_disparity", help="Normalization type for depth")
    parser.add_argument("--truncnorm_min", type=float, default=0.1, help="Minimum value for truncated normalization")

    args = parser.parse_args()
    env_local_rank = int(os.environ.get("LOCAL_RANK", -1))
    if env_local_rank != -1 and env_local_rank != args.local_rank:
        args.local_rank = env_local_rank

    # Sanity checks
    if args.train_data_dir_hypersim is None and args.train_data_dir_vkitti is None:
        raise ValueError("Need either a dataset folder for hypersim or vkitti.")

    return args

def main():
    args = parse_args()

    if args.report_to == "wandb" and args.hub_token is not None:
        raise ValueError(
            "You cannot use both --report_to=wandb and --hub_token due to a security risk of exposing your token."
            " Please use `huggingface-cli login` to authenticate with the Hub."
        )

    logging_dir = os.path.join(args.output_dir, args.logging_dir)

    accelerator_project_config = ProjectConfiguration(project_dir=args.output_dir, logging_dir=logging_dir)

    kwargs = InitProcessGroupKwargs(timeout=timedelta(seconds=36000))
    accelerator = Accelerator(
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        mixed_precision=args.mixed_precision,
        log_with=args.report_to,
        project_config=accelerator_project_config,
        kwargs_handlers=[kwargs]
    )

    # Disable AMP for MPS.
    if torch.backends.mps.is_available():
        accelerator.native_amp = False

    # Make one log on every process with the configuration for debugging.
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        datefmt="%m/%d/%Y %H:%M:%S",
        level=logging.INFO,
    )
    logger.info(accelerator.state, main_process_only=False)
    if accelerator.is_local_main_process:
        datasets.utils.logging.set_verbosity_warning()
        transformers.utils.logging.set_verbosity_warning()
        diffusers.utils.logging.set_verbosity_info()
    else:
        datasets.utils.logging.set_verbosity_error()
        transformers.utils.logging.set_verbosity_error()
        diffusers.utils.logging.set_verbosity_error()

    # If passed along, set the training seed now.
    if args.seed is not None:
        set_seed(args.seed)

    # Handle the repository creation
    if accelerator.is_main_process:
        if args.output_dir is not None:
            os.makedirs(args.output_dir, exist_ok=True)

        if args.push_to_hub:
            repo_id = create_repo(
                repo_id=args.hub_model_id or Path(args.output_dir).name, exist_ok=True, token=args.hub_token
            ).repo_id

    # Load scheduler, tokenizer and models.
    tokenizer = CLIPTokenizer.from_pretrained(
        args.pretrained_model_name_or_path, subfolder="tokenizer", revision=args.revision
    )
    text_encoder = CLIPTextModel.from_pretrained(
        args.pretrained_model_name_or_path, subfolder="text_encoder", revision=args.revision, variant=args.variant
    )
    vae = AutoencoderKL.from_pretrained(
        args.pretrained_model_name_or_path, subfolder="vae", revision=args.revision, variant=args.variant
    )
    unet = UNet2DConditionModel.from_pretrained(
        args.pretrained_model_name_or_path, subfolder="unet", revision=args.revision, variant=args.variant
    )

    # Freeze vae and text_encoder and set unet to trainable
    vae.requires_grad_(False)
    text_encoder.requires_grad_(False)
    unet.train()

    # Create EMA for the unet (disabled for deterministic training)
    use_ema = False  # Deterministic training doesn't use EMA
    if use_ema:
        ema_unet = UNet2DConditionModel.from_pretrained(
            args.pretrained_model_name_or_path, subfolder="unet", revision=args.revision, variant=args.variant
        )
        ema_unet = EMAModel(ema_unet.parameters(), model_cls=UNet2DConditionModel, model_config=ema_unet.config)

    if args.enable_xformers_memory_efficient_attention:
        if is_xformers_available():
            import xformers

            xformers_version = version.parse(xformers.__version__)
            if xformers_version == version.parse("0.0.16"):
                logger.warn(
                    "xFormers 0.0.16 cannot be used for training in some GPUs. If you observe problems during training, please update xFormers to at least 0.0.17. See https://huggingface.co/docs/diffusers/main/en/optimization/xformers for more details."
                )
            unet.enable_xformers_memory_efficient_attention()
        else:
            raise ValueError("xformers is not available. Make sure it is installed correctly")

    # `accelerate` 0.16.0 will have better support for customized saving
    if version.parse(accelerate.__version__) >= version.parse("0.16.0"):
        # create custom saving & loading hooks so that `accelerator.save_state(...)` serializes in a nice format
        def save_model_hook(models, weights, output_dir):
            if accelerator.is_main_process:
                if use_ema:
                    ema_unet.save_pretrained(os.path.join(output_dir, "unet_ema"))

                for i, model in enumerate(models):
                    model.save_pretrained(os.path.join(output_dir, "unet"))

                    # make sure to pop weight so that corresponding model is not saved again
                    weights.pop()

        def load_model_hook(models, input_dir):
            if use_ema:
                load_model = EMAModel.from_pretrained(os.path.join(input_dir, "unet_ema"), UNet2DConditionModel)
                ema_unet.load_state_dict(load_model.state_dict())
                ema_unet.to(accelerator.device)
                del load_model

            for i in range(len(models)):
                # pop models so that they are not loaded again
                model = models.pop()

                # load diffusers style into model
                load_model = UNet2DConditionModel.from_pretrained(input_dir, subfolder="unet")
                model.register_to_config(**load_model.config)

                model.load_state_dict(load_model.state_dict())
                del load_model

        accelerator.register_save_state_pre_hook(save_model_hook)
        accelerator.register_load_state_pre_hook(load_model_hook)

    if args.gradient_checkpointing:
        unet.enable_gradient_checkpointing()

    # Enable TF32 for faster training on Ampere GPUs,
    # cf https://pytorch.org/docs/stable/notes/cuda.html#tensorfloat-32-tf32-on-ampere-devices
    if args.allow_tf32:
        torch.backends.cuda.matmul.allow_tf32 = True

    if args.scale_lr:
        args.learning_rate = (
            args.learning_rate * args.gradient_accumulation_steps * args.train_batch_size * accelerator.num_processes
        )

    # Initialize the optimizer
    if args.use_8bit_adam:
        try:
            import bitsandbytes as bnb
        except ImportError:
            raise ImportError(
                "Please install bitsandbytes to use 8-bit Adam. You can do so by running `pip install bitsandbytes`"
            )

        optimizer_cls = bnb.optim.AdamW8bit
    else:
        optimizer_cls = torch.optim.AdamW

    optimizer = optimizer_cls(
        unet.parameters(),
        lr=args.learning_rate,
        betas=(args.adam_beta1, args.adam_beta2),
        weight_decay=args.adam_weight_decay,
        eps=args.adam_epsilon,
    )

    # -------------------- Dataset --------------------
    # Dataset1: Hypersim
    if args.train_data_dir_hypersim is not None:
        train_dataset_hypersim, collate_fn_hypersim = get_hypersim_dataset_depth_normal(
            args.train_data_dir_hypersim,
            args.resolution_hypersim,
            random_flip=args.random_flip
        )
        train_dataloader_hypersim = torch.utils.data.DataLoader(
            train_dataset_hypersim,
            shuffle=True,
            collate_fn=collate_fn_hypersim,
            batch_size=args.train_batch_size,
            num_workers=args.dataloader_num_workers,
            pin_memory=True
        )
    else:
        train_dataloader_hypersim = None

    # Dataset2: VKITTI
    if args.train_data_dir_vkitti is not None:
        transform_vkitti = VKITTITransform(random_flip=args.random_flip)
        train_dataset_vkitti = VKITTIDataset(
            args.train_data_dir_vkitti,
            transform_vkitti,
            args.norm_type,
            truncnorm_min=args.truncnorm_min
        )
        train_dataloader_vkitti = torch.utils.data.DataLoader(
            train_dataset_vkitti,
            shuffle=True,
            collate_fn=collate_fn_vkitti,
            batch_size=args.train_batch_size,
            num_workers=args.dataloader_num_workers,
            pin_memory=True
        )
    else:
        train_dataloader_vkitti = None

    # Use primary dataset for training loop
    primary_dataloader = train_dataloader_hypersim if train_dataloader_hypersim is not None else train_dataloader_vkitti

    # Lr_scheduler and math around the number of training steps.
    overrode_max_train_steps = False
    num_update_steps_per_epoch = math.ceil(len(primary_dataloader) / args.gradient_accumulation_steps)
    assert args.max_train_steps is not None or args.num_train_epochs is not None, "max_train_steps or num_train_epochs should be provided"
    if args.max_train_steps is None:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
        overrode_max_train_steps = True

    lr_scheduler = get_scheduler(
        args.lr_scheduler,
        optimizer=optimizer,
        num_warmup_steps=args.lr_warmup_steps * accelerator.num_processes,
        num_training_steps=args.max_train_steps * accelerator.num_processes,
    )

    # Prepare everything with our `accelerator`.
    if train_dataloader_hypersim is not None and train_dataloader_vkitti is not None:
        unet, optimizer, train_dataloader_hypersim, train_dataloader_vkitti, lr_scheduler = accelerator.prepare(
            unet, optimizer, train_dataloader_hypersim, train_dataloader_vkitti, lr_scheduler
        )
    elif train_dataloader_hypersim is not None:
        unet, optimizer, train_dataloader_hypersim, lr_scheduler = accelerator.prepare(
            unet, optimizer, train_dataloader_hypersim, lr_scheduler
        )
        train_dataloader_vkitti = None
    else:
        unet, optimizer, train_dataloader_vkitti, lr_scheduler = accelerator.prepare(
            unet, optimizer, train_dataloader_vkitti, lr_scheduler
        )
        train_dataloader_hypersim = None

    # For mixed precision training we cast all non-trainable weights (vae, non-lora text_encoder and non-lora unet) to half-precision
    # as these weights are only used for inference, keeping weights in full precision is not required.
    weight_dtype = torch.float32
    if accelerator.mixed_precision == "fp16":
        weight_dtype = torch.float16
        args.mixed_precision = accelerator.mixed_precision
    elif accelerator.mixed_precision == "bf16":
        weight_dtype = torch.bfloat16
        args.mixed_precision = accelerator.mixed_precision

    # Move text_encode and vae to gpu and cast to weight_dtype
    text_encoder.to(accelerator.device, dtype=weight_dtype)
    vae.to(accelerator.device, dtype=weight_dtype)

    # We need to recalculate our total training steps as the size of the training dataloader may have changed.
    primary_dataloader = train_dataloader_hypersim if train_dataloader_hypersim is not None else train_dataloader_vkitti
    num_update_steps_per_epoch = math.ceil(len(primary_dataloader) / args.gradient_accumulation_steps)
    if overrode_max_train_steps:
        args.max_train_steps = args.num_train_epochs * num_update_steps_per_epoch
    # Afterwards we recalculate our number of training epochs
    args.num_train_epochs = math.ceil(args.max_train_steps / num_update_steps_per_epoch)

    # We need to initialize the trackers we use, and also store our configuration.
    # The trackers initializes automatically on the main process.
    if accelerator.is_main_process:
        tracker_config = dict(vars(args))
        accelerator.init_trackers(args.tracker_project_name, tracker_config)

    # Train!
    total_batch_size = args.train_batch_size * accelerator.num_processes * args.gradient_accumulation_steps

    logger.info("***** Running training *****")
    logger.info(f"  Num examples = {len(primary_dataloader.dataset)}")
    logger.info(f"  Num Epochs = {args.num_train_epochs}")
    logger.info(f"  Instantaneous batch size per device = {args.train_batch_size}")
    logger.info(f"  Total train batch size (w. parallel, distributed & accumulation) = {total_batch_size}")
    logger.info(f"  Gradient Accumulation steps = {args.gradient_accumulation_steps}")
    logger.info(f"  Total optimization steps = {args.max_train_steps}")
    logger.info(f"  Task name: {args.task_name}")
    logger.info(f"  RGB timestep = {args.timestep_rgb}")
    logger.info(f"  Depth timestep = {args.timestep_depth}")
    logger.info(f"  Normal timestep = {args.timestep_normal}")
    logger.info(f"  Task ratios - RGB: {args.task_ratio_rgb}, Depth: {args.task_ratio_depth}, Normal: {args.task_ratio_normal}")
    logger.info(f"  Loss weights - RGB: {args.loss_weight_rgb}, Depth: {args.loss_weight_depth}, Normal: {args.loss_weight_normal}")
    logger.info(f"  Is Full Evaluation?: {args.FULL_EVALUATION}")
    logger.info(f"  Output Workspace: {args.output_dir}")

    global_step = 0
    first_epoch = 0

    # Potentially load in the weights and states from a previous save
    if args.resume_from_checkpoint:
        if args.resume_from_checkpoint != "latest":
            path = os.path.basename(args.resume_from_checkpoint)
        else:
            # Get the most recent checkpoint
            dirs = os.listdir(args.output_dir)
            dirs = [d for d in dirs if d.startswith("checkpoint")]
            dirs = sorted(dirs, key=lambda x: int(x.split("-")[1]))
            path = dirs[-1] if len(dirs) > 0 else None

        if path is None:
            accelerator.print(
                f"Checkpoint '{args.resume_from_checkpoint}' does not exist. Starting a new training run."
            )
            args.resume_from_checkpoint = None
            initial_global_step = 0
        else:
            accelerator.print(f"Resuming from checkpoint {path}")
            accelerator.load_state(os.path.join(args.output_dir, path))
            global_step = int(path.split("-")[1])

            initial_global_step = global_step
            first_epoch = global_step // num_update_steps_per_epoch

    else:
        initial_global_step = 0

    progress_bar = tqdm(
        range(0, args.max_train_steps),
        initial=initial_global_step,
        desc="Steps",
        # Only show the progress bar once on each machine.
        disable=not accelerator.is_local_main_process,
    )

    # Load empty text embeddings for conditioning
    empty_text_embeddings = None
    if text_encoder is not None:
        # Encode empty text prompt
        text_inputs = tokenizer(
            "",
            padding="max_length",
            max_length=tokenizer.model_max_length,
            truncation=True,
            return_tensors="pt",
        )
        text_input_ids = text_inputs.input_ids.to(accelerator.device)
        empty_text_embeddings = text_encoder(text_input_ids, return_dict=False)[0]
    else:
        # Load from file if text encoder is not available
        empty_text_embed_path = getattr(args, 'empty_text_embed_path', None)
        if empty_text_embed_path and os.path.exists(empty_text_embed_path):
            empty_text_embeddings = torch.load(empty_text_embed_path, map_location=accelerator.device)
        else:
            raise ValueError("Text encoder is None and no empty text embeddings file found")

    for epoch in range(first_epoch, args.num_train_epochs):
        train_loss = 0.0
        log_rgb_loss = 0.0
        log_depth_loss = 0.0
        log_normal_loss = 0.0

        # Setup iterators for mixed dataset training
        if args.mix_dataset and train_dataloader_hypersim is not None and train_dataloader_vkitti is not None:
            iter_hypersim = iter(train_dataloader_hypersim)
            iter_vkitti = iter(train_dataloader_vkitti)
            primary_length = len(train_dataloader_hypersim)
        else:
            primary_length = len(primary_dataloader)

        for _ in range(primary_length):
            # Get batch from appropriate dataset
            if args.mix_dataset and train_dataloader_hypersim is not None and train_dataloader_vkitti is not None:
                if random.random() < args.prob_hypersim:
                    batch = next(iter_hypersim)
                else:
                    # Handle VKITTI dataset exhaustion
                    try:
                        batch = next(iter_vkitti)
                    except StopIteration:
                        iter_vkitti = iter(train_dataloader_vkitti)
                        batch = next(iter_vkitti)
            else:
                batch = next(iter(primary_dataloader))

            with accelerator.accumulate(unet):
                # Convert RGB images to latent space
                rgb_latents = vae.encode(batch["pixel_values"].to(weight_dtype)).latent_dist.sample()
                rgb_latents = rgb_latents * vae.config.scaling_factor

                # MTL: Dynamic timestep assignment and target selection
                batch_size = batch["pixel_values"].shape[0]
                timesteps, target_images, task_types = assign_timesteps_and_targets(
                    batch, batch_size, args, accelerator.device
                )

                # Convert target images to latent space
                target_latents = vae.encode(target_images.to(weight_dtype)).latent_dist.sample()
                target_latents = target_latents * vae.config.scaling_factor

                # MTL: Create valid masks for each sample based on task type
                valid_masks = []
                for i, task_type in enumerate(task_types):
                    if task_type in ["depth"] and batch.get("valid_mask_values", None) is not None:
                        # Use depth-specific valid mask
                        valid_mask_for_latent = batch["valid_mask_values"][i:i+1]
                        sky_mask_for_latent = batch.get("sky_mask_values", None)
                        if sky_mask_for_latent is not None:
                            valid_mask_for_latent = valid_mask_for_latent + sky_mask_for_latent[i:i+1]

                        valid_mask_for_latent = valid_mask_for_latent.bool()
                        invalid_mask = ~valid_mask_for_latent
                        valid_mask_down = ~torch.max_pool2d(invalid_mask.float(), 8, 8).bool()
                        valid_mask_down = valid_mask_down.repeat((1, 4, 1, 1))
                    else:
                        # Default mask for RGB and normal tasks
                        valid_mask_down = torch.ones_like(target_latents[i:i+1]).bool()

                    valid_masks.append(valid_mask_down)

                # Concatenate all masks
                valid_masks = torch.cat(valid_masks, dim=0) if valid_masks else None

                # DETERMINISTIC APPROACH: Use clean RGB latents as UNet input (no noise addition)
                unet_input = rgb_latents  # Only RGB latents, no concatenation with noisy targets

                # Use the empty text embeddings for conditioning
                encoder_hidden_states = empty_text_embeddings.repeat(batch_size, 1, 1)

                # Get the target for loss (clean target latents)
                target = target_latents

                # MTL: UNet prediction with task conditioning via timesteps
                model_pred = unet(unet_input, timesteps, encoder_hidden_states, return_dict=False)[0]

                # MTL: Compute multi-task loss using the new function
                loss, rgb_loss, depth_loss, normal_loss, weighted_rgb_loss, weighted_depth_loss, weighted_normal_loss = compute_multi_task_loss(
                    model_pred, target, task_types, valid_masks, args
                )

                # Gather losses for logging
                avg_rgb_loss = accelerator.gather(rgb_loss.repeat(args.train_batch_size)).mean()
                avg_depth_loss = accelerator.gather(depth_loss.repeat(args.train_batch_size)).mean()
                avg_normal_loss = accelerator.gather(normal_loss.repeat(args.train_batch_size)).mean()

                # Log losses
                log_rgb_loss += avg_rgb_loss.item() / args.gradient_accumulation_steps
                log_depth_loss += avg_depth_loss.item() / args.gradient_accumulation_steps
                log_normal_loss += avg_normal_loss.item() / args.gradient_accumulation_steps
                train_loss = log_rgb_loss + log_depth_loss + log_normal_loss

                # Backpropagate
                accelerator.backward(loss)
                if accelerator.sync_gradients:
                    accelerator.clip_grad_norm_(unet.parameters(), args.max_grad_norm)
                optimizer.step()
                lr_scheduler.step()
                optimizer.zero_grad()

            # MTL: Updated logging for multi-task learning
            logs = {"SL": loss.detach().item(),
                    "SL_D": depth_loss.detach().item(),
                    "SL_N": normal_loss.detach().item(),
                    "SL_R": rgb_loss.detach().item(),
                    "WD": weighted_depth_loss.detach().item(),
                    "WN": weighted_normal_loss.detach().item(),
                    "WR": weighted_rgb_loss.detach().item(),
                    "lr": lr_scheduler.get_last_lr()[0]}
            progress_bar.set_postfix(**logs)

            # Checks if the accelerator has performed an optimization step behind the scenes
            if accelerator.sync_gradients:
                progress_bar.update(1)
                global_step += 1
                # MTL: Updated logging for multi-task learning
                total_samples = sum(TIMESTEP_FREQUENCY.values())
                if total_samples > 0:
                    rgb_freq = TIMESTEP_FREQUENCY['rgb'] / total_samples * 100
                    depth_freq = TIMESTEP_FREQUENCY['depth'] / total_samples * 100
                    normal_freq = TIMESTEP_FREQUENCY['normal'] / total_samples * 100

                    accelerator.log({"train_loss": train_loss,
                                    "rgb_loss": log_rgb_loss,
                                    "depth_loss": log_depth_loss,
                                    "normal_loss": log_normal_loss,
                                    "rgb_freq": rgb_freq,
                                    "depth_freq": depth_freq,
                                    "normal_freq": normal_freq},
                                     step=global_step)

                # Reset loss accumulators
                train_loss = 0.0
                log_rgb_loss = 0.0
                log_depth_loss = 0.0
                log_normal_loss = 0.0

                checkpointing_steps = args.checkpointing_steps
                validation_steps = args.validation_steps

                if isinstance(checkpointing_steps, int):
                    if global_step % checkpointing_steps == 0:
                        if args.checkpoints_total_limit is not None:
                            checkpoints = os.listdir(args.output_dir)
                            checkpoints = [d for d in checkpoints if d.startswith("checkpoint")]
                            checkpoints = sorted(checkpoints, key=lambda x: int(x.split("-")[1]))

                            # before we save the new checkpoint, we need to have at _most_ `checkpoints_total_limit - 1` checkpoints
                            if len(checkpoints) >= args.checkpoints_total_limit:
                                num_to_remove = len(checkpoints) - args.checkpoints_total_limit + 1
                                removing_checkpoints = checkpoints[0:num_to_remove]

                                logger.info(
                                    f"{len(checkpoints)} checkpoints already exist, removing {len(removing_checkpoints)} checkpoints"
                                )
                                logger.info(f"removing checkpoints: {', '.join(removing_checkpoints)}")

                                for removing_checkpoint in removing_checkpoints:
                                    removing_checkpoint = os.path.join(args.output_dir, removing_checkpoint)
                                    shutil.rmtree(removing_checkpoint)

                        save_path = os.path.join(args.output_dir, f"checkpoint-{global_step}")
                        accelerator.save_state(save_path)
                        logger.info(f"Saved state to {save_path}")

                if global_step % validation_steps == 0:
                    log_validation(
                        vae,
                        text_encoder,
                        tokenizer,
                        unet,
                        args,
                        accelerator,
                        weight_dtype,
                        global_step,
                    )

                    # Print timestep statistics
                    print_timestep_statistics(global_step)

            if global_step >= args.max_train_steps:
                break

    # Create the pipeline using the trained modules and save it.
    accelerator.wait_for_everyone()
    if accelerator.is_main_process:
        unet = accelerator.unwrap_model(unet)

        pipeline = LotusDPipeline.from_pretrained(
            args.pretrained_model_name_or_path,
            text_encoder=text_encoder,
            vae=vae,
            unet=unet,
            revision=args.revision,
            variant=args.variant,
        )
        pipeline.save_pretrained(args.output_dir)

        # Run a final round of inference.
        if args.validation_images is not None:
            logger.info("Running inference for collecting generated images...")
            pipeline = pipeline.to(accelerator.device)
            pipeline.set_progress_bar_config(disable=True)

            if args.enable_xformers_memory_efficient_attention:
                pipeline.enable_xformers_memory_efficient_attention()

            if args.seed is None:
                generator = None
            else:
                generator = torch.Generator(device=accelerator.device).manual_seed(args.seed)

            validation_images = glob(os.path.join(args.validation_images, "*.jpg")) + glob(os.path.join(args.validation_images, "*.png"))
            validation_images = sorted(validation_images)

            for i, validation_image_path in enumerate(validation_images):
                validation_image = Image.open(validation_image_path).convert("RGB")
                validation_image = np.array(validation_image).astype(np.float32)
                validation_image = torch.tensor(validation_image).permute(2,0,1).unsqueeze(0)
                validation_image = validation_image / 127.5 - 1.0
                validation_image = validation_image.to(accelerator.device)

                # Test all three tasks
                for task_type, timestep in [("depth", args.timestep_depth), ("normal", args.timestep_normal), ("rgb", args.timestep_rgb)]:
                    task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(accelerator.device)
                    task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)

                    with torch.autocast("cuda"):
                        image = pipeline(
                            rgb_in=validation_image,
                            task_emb=task_emb,
                            prompt="",
                            timesteps=[timestep],
                            generator=generator,
                            output_type="np"
                        ).images[0]

                    # Save the result
                    if task_type == "depth":
                        image = colorize_depth_map(image.mean(axis=-1))
                    else:
                        image = (image * 255).astype(np.uint8)

                    image = Image.fromarray(image)
                    image.save(os.path.join(args.output_dir, f"final_{task_type}_{i}.png"))

        if args.push_to_hub:
            upload_folder(
                repo_id=repo_id,
                folder_path=args.output_dir,
                commit_message="End of training",
                ignore_patterns=["step_*", "epoch_*"],
            )

    accelerator.end_training()

if __name__ == "__main__":
    main()
