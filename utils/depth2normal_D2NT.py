from PIL import Image
import numpy as np
import torch
import cv2
import argparse
import os
from tqdm import tqdm

# DAG filter implementation from depth-to-normal-translator
def get_filter(Z):
    """Basic gradient filter"""
    kernel_Gx = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) / 8.0
    kernel_Gy = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) / 8.0
    
    Gu = cv2.filter2D(Z, -1, kernel_Gx)
    Gv = cv2.filter2D(Z, -1, kernel_Gy)
    return Gu, Gv

def soft_min(laplace_map, base, direction):
    """Soft minimum function for DAG filters"""
    h, w = laplace_map.shape
    eps = 1e-8  # to avoid division by zero

    lap_power = np.power(base, -laplace_map)
    if direction == 0:  # horizontal
        lap_pow_l = np.hstack([np.zeros((h, 1)), lap_power[:, :-1]])
        lap_pow_r = np.hstack([lap_power[:, 1:], np.zeros((h, 1))])
        return (lap_pow_l + eps * 0.5) / (eps + lap_pow_l + lap_pow_r), \
               (lap_pow_r + eps * 0.5) / (eps + lap_pow_l + lap_pow_r)

    elif direction == 1:  # vertical
        lap_pow_u = np.vstack([np.zeros((1, w)), lap_power[:-1, :]])
        lap_pow_d = np.vstack([lap_power[1:, :], np.zeros((1, w))])
        return (lap_pow_u + eps / 2) / (eps + lap_pow_u + lap_pow_d), \
               (lap_pow_d + eps / 2) / (eps + lap_pow_u + lap_pow_d)

def get_DAG_filter(Z, lap_conf='DLF-alpha', base=2):
    """Discontinuity-Aware Gradient filter"""
    # Define kernels
    kernel_Gx = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) / 8.0
    kernel_Gy = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) / 8.0
    
    lap_ker_alpha = np.array([[0, -1, 0], [-1, 4, -1], [0, -1, 0]]) / 4.0
    
    # Calculate gradients
    grad_l = cv2.filter2D(Z, -1, np.array([[0, 0, 0], [-1, 1, 0], [0, 0, 0]]))
    grad_r = cv2.filter2D(Z, -1, np.array([[0, 0, 0], [0, 1, -1], [0, 0, 0]]))
    grad_u = cv2.filter2D(Z, -1, np.array([[0, -1, 0], [0, 1, 0], [0, 0, 0]]))
    grad_d = cv2.filter2D(Z, -1, np.array([[0, 0, 0], [0, 1, 0], [0, -1, 0]]))

    # Calculate laplace along 2 directions
    if lap_conf == 'DLF-alpha':
        lap_hor = abs(cv2.filter2D(Z, -1, lap_ker_alpha))
        lap_ver = abs(cv2.filter2D(Z, -1, lap_ker_alpha))
    else:
        lap_hor = abs(grad_l - grad_r)
        lap_ver = abs(grad_u - grad_d)

    # Get lambda maps using soft_min
    lambda_map1, lambda_map2 = soft_min(lap_hor, base, 0)  # horizontal
    lambda_map3, lambda_map4 = soft_min(lap_ver, base, 1)   # vertical

    # Compute final gradients
    Gu = lambda_map1 * grad_l + lambda_map2 * grad_r
    Gv = lambda_map3 * grad_u + lambda_map4 * grad_d
    return Gu, Gv

def vector_normalization(normal):
    """Normalize normal vectors"""
    norm = np.linalg.norm(normal, axis=2, keepdims=True)
    norm[norm < 1e-12] = 1e-12
    return normal / norm

def MRF_optim(depth, n_est, lap_conf='DLF-alpha'):
    """MRF-based normal refinement"""
    h, w = depth.shape
    n_x, n_y, n_z = n_est[:, :, 0], n_est[:, :, 1], n_est[:, :, 2]
    
    # Define kernels
    laplace_hor = np.array([[0, 0, 0], [-1, 2, -1], [0, 0, 0]])
    laplace_ver = np.array([[0, -1, 0], [0, 2, 0], [0, -1, 0]])
    lap_ker_alpha = np.array([[0, -1, 0], [-1, 4, -1], [0, -1, 0]]) / 4.0
    
    if lap_conf == 'DLF-alpha':
        Z_laplace_hor = abs(cv2.filter2D(depth, -1, lap_ker_alpha))
        Z_laplace_ver = abs(cv2.filter2D(depth, -1, lap_ker_alpha))
    else:
        Z_laplace_hor = abs(cv2.filter2D(depth, -1, laplace_hor))
        Z_laplace_ver = abs(cv2.filter2D(depth, -1, laplace_ver))
    
    # Apply MRF optimization (simplified version)
    # In practice, this would involve iterative optimization
    # For now, we apply a simple smoothing based on depth discontinuities
    weight_hor = np.exp(-Z_laplace_hor * 10)
    weight_ver = np.exp(-Z_laplace_ver * 10)
    
    # Simple bilateral filtering on normals
    n_x_smooth = cv2.bilateralFilter(n_x.astype(np.float32), 5, 0.1, 1.0)
    n_y_smooth = cv2.bilateralFilter(n_y.astype(np.float32), 5, 0.1, 1.0)
    n_z_smooth = cv2.bilateralFilter(n_z.astype(np.float32), 5, 0.1, 1.0)
    
    # Combine original and smoothed based on weights
    n_x_final = n_x * (1 - weight_hor) + n_x_smooth * weight_hor
    n_y_final = n_y * (1 - weight_ver) + n_y_smooth * weight_ver
    n_z_final = n_z

    return np.stack([n_x_final, n_y_final, n_z_final], axis=2)

def count_data(root_dir, scenes):
    """Count total number of depth files to process"""
    print('root dir and scenes: ', root_dir, scenes)
    count = 0
    for scene in scenes:
        for dirpath, dirnames, filenames in os.walk(root_dir):
            for filename in filenames:
                if filename.startswith("depth") and f'Scene{scene}' in dirpath:
                    count += 1
    return count

def args_parser():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_path", required=True, help="Path to Virtual KITTI dataset")
    parser.add_argument("--depth_min", type=float, default=1e-3, help="Minimum depth value")
    parser.add_argument("--depth_max", type=float, default=80.0, help="Maximum depth value")
    parser.add_argument("--batch_size", type=int, default=4, help="Batch size for processing")
    parser.add_argument("--scenes", type=str, nargs="+",
                        default=['01', '02', '06', '18', '20'],
                        help="Scene numbers to process")
    parser.add_argument("--version", type=str, default='d2nt_v3',
                        choices=['d2nt_basic', 'd2nt_v2', 'd2nt_v3'],
                        help="DAG version to use")

    return parser.parse_args()

def process_depth_to_normal_DAG(depth, intrinsics, version='d2nt_v3'):
    """
    Convert depth map to normal map using DAG method

    Args:
        depth: depth map in meters (H, W)
        intrinsics: camera intrinsics [fx, fy, cx, cy]
        version: DAG version to use

    Returns:
        normal: normal map in camera coordinates (H, W, 3)
    """
    # Convert depth to centimeters for DAG processing
    depth_cm = depth * 100.0

    # Get camera parameters
    cam_fx, cam_fy, u0, v0 = intrinsics[0], intrinsics[1], intrinsics[2], intrinsics[3]
    h, w = depth_cm.shape

    # Create coordinate maps
    u_map = np.ones((h, 1)) * np.arange(1, w + 1) - u0                # u-u0
    v_map = np.arange(1, h + 1).reshape(h, 1) * np.ones((1, w)) - v0  # v-v0

    # Apply DAG Depth to Normals
    if version == 'd2nt_basic':
        Gu, Gv = get_filter(depth_cm)
    else:
        Gu, Gv = get_DAG_filter(depth_cm)

    # Depth to Normal Translation
    est_nx = Gu * cam_fx
    est_ny = Gv * cam_fy
    est_nz = -(depth_cm + v_map * Gv + u_map * Gu)

    # Ensure all components have the same shape and dtype
    est_nx = est_nx.astype(np.float32)
    est_ny = est_ny.astype(np.float32)
    est_nz = est_nz.astype(np.float32)

    est_normal = np.stack([est_nx, est_ny, est_nz], axis=2)

    # Vector normalization
    est_normal = vector_normalization(est_normal)

    # MRF-based Normal Refinement for v3
    if version == 'd2nt_v3':
        est_normal = MRF_optim(depth_cm, est_normal)

    # Redirect normals against camera (to match expected coordinate system)
    est_normal = est_normal * -1  # [H,W,3]

    return est_normal

def normal_to_rgb_lotus(normal):
    """
    Convert normal map to RGB format compatible with Lotus training

    Args:
        normal: normal map in range [-1, 1] (H, W, 3)

    Returns:
        normal_rgb: RGB image in range [0, 255] (H, W, 3)
    """
    # Normalize vectors
    normal_norm = np.linalg.norm(normal, axis=2, keepdims=True)
    normal_norm[normal_norm < 1e-12] = 1e-12
    normal = normal / normal_norm

    # Convert to RGB format: [-1, 1] -> [0, 255]
    normal_rgb = ((normal + 1.0) * 0.5 * 255).astype(np.uint8)

    return normal_rgb

if __name__ == "__main__":

    args = args_parser()

    data_path = args.data_path
    depth_min = args.depth_min
    depth_max = args.depth_max
    batch_size = args.batch_size
    version = args.version

    # scenes = ['01', '02', '06', '18', '20']
    scenes = args.scenes
    print(f"Processing scenes: {scenes} using DAG version: {version}")

    num_data = count_data(data_path, scenes)
    print(f'Total number of data: {num_data}')

    conditions = [
            '15-deg-left', '15-deg-right', '30-deg-left', '30-deg-right', 'clone',
            'fog', 'morning', 'overcast', 'rain', 'sunset'
        ]
    cameras = ['0', '1']
    # SceneX/Y/frames/depth/Camera_Z/depth_%05d.png
    # SceneX/Y/intrinsic.txt

    pbar = tqdm(total=num_data)

    for scene in scenes:
        for cond in conditions:
            intrinsic_file = os.path.join(data_path, f'Scene{scene}/{cond}/intrinsic.txt')

            # Skip if intrinsic file doesn't exist
            if not os.path.exists(intrinsic_file):
                continue

            with open(intrinsic_file, 'r') as file:
                lines = file.readlines()
                frames_x_cameras = len(lines)-1
                intrinsics = np.zeros((frames_x_cameras//len(cameras), len(cameras), 4))
                for line in lines[1:]:
                    line = line.strip().split(" ")
                    frame_id = int(line[0])
                    camera_id = int(line[1])
                    k_00 = float(line[2])
                    k_11 = float(line[3])
                    k_02 = float(line[4])
                    k_12 = float(line[5])
                    intrinsics[frame_id][camera_id] = np.array([k_00, k_11, k_02, k_12])

            for cam in cameras:
                depth_dir = os.path.join(data_path, f'Scene{scene}/{cond}/frames/depth/Camera_{cam}')

                # Skip if depth directory doesn't exist
                if not os.path.exists(depth_dir):
                    continue

                depth_files = os.listdir(depth_dir)

                num_batch = len(depth_files) // batch_size
                batches = [batch_size]*num_batch
                res = len(depth_files) % batch_size
                if res > 0:
                    num_batch += 1
                    batches += [res]

                for idx_b, batch in enumerate(batches):

                    for i in range(batch):
                        depth_file_idx = idx_b * batch_size + i
                        if depth_file_idx >= len(depth_files):
                            break

                        depth_file = depth_files[depth_file_idx]
                        depth_path = os.path.join(depth_dir, depth_file)

                        # Load depth image
                        depth = cv2.imread(depth_path, cv2.IMREAD_ANYCOLOR | cv2.IMREAD_ANYDEPTH)
                        if depth is None:
                            print(f"Warning: Could not load depth file {depth_path}")
                            continue

                        depth = depth.astype(np.float32) / 100.0  # cm -> m

                        # Get frame ID and camera intrinsics
                        frame_id = int(depth_file.split(".")[0].split("_")[-1])
                        if frame_id >= intrinsics.shape[0]:
                            print(f"Warning: Frame ID {frame_id} out of range for intrinsics")
                            continue

                        cam_intrinsics = intrinsics[frame_id][int(cam)]

                        # Process depth to normal using DAG method
                        normal = process_depth_to_normal_DAG(depth, cam_intrinsics, version)

                        # Convert to Lotus-compatible RGB format
                        normal_rgb = normal_to_rgb_lotus(normal)

                        # Create PIL Image and save
                        norm_rgb_pil = Image.fromarray(normal_rgb, "RGB")

                        # Generate output path (replace "depth" with "normal")
                        norm_rgb_save_path = depth_path.replace("depth", "normal")
                        os.makedirs(os.path.dirname(norm_rgb_save_path), exist_ok=True)
                        norm_rgb_pil.save(norm_rgb_save_path)

                    pbar.update(batch)

    pbar.close()
    print(f"Normal map generation completed using DAG method ({version})")
