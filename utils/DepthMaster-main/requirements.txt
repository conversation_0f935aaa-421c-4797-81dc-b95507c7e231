absl-py==2.1.0
accelerate==0.31.0
aiohttp==3.9.5
aiosignal==1.3.1
antlr4-python3-runtime==4.9.3
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1698341106958/work
async-timeout==4.0.3
attrs==23.2.0
bitsandbytes==0.43.1
certifi==2024.6.2
charset-normalizer==3.3.2
click==8.1.7
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1710320294760/work
contourpy==1.2.1
cycler==0.12.1
datasets==2.19.2
debugpy @ file:///croot/debugpy_1690905042057/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1641555617451/work
diffusers==0.29.0
dill==0.3.8
docker-pycreds==0.4.0
einops==0.8.0
entrypoints @ file:///home/<USER>/feedstock_root/build_artifacts/entrypoints_1643888246732/work
exceptiongroup @ file:///home/<USER>/feedstock_root/build_artifacts/exceptiongroup_1704921103267/work
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1698579936712/work
filelock==3.13.1
fonttools==4.53.0
frozenlist==1.4.1
fsspec==2024.2.0
gitdb==4.0.11
GitPython==3.1.43
grpcio==1.64.1
h5py==3.11.0
huggingface-hub==0.27.1
idna==3.7
imageio==2.34.1
imgaug==0.4.0
importlib_metadata==7.1.0
ipykernel @ file:///home/<USER>/feedstock_root/build_artifacts/ipykernel_1717717528849/work
ipython @ file:///home/<USER>/feedstock_root/build_artifacts/ipython_1717182742060/work
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1696326070614/work
Jinja2==3.1.3
jupyter-client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1654730843242/work
jupyter_core @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_core_1710257277185/work
kiwisolver==1.4.5
lazy_loader==0.4
lightning-utilities==0.11.2
Markdown==3.6
MarkupSafe==2.1.5
matplotlib==3.9.0
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1713250518406/work
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1705850609492/work
networkx==3.2.1
numpy==1.26.3
nvidia-cublas-cu11==*********
nvidia-cuda-cupti-cu11==11.8.87
nvidia-cuda-nvrtc-cu11==11.8.89
nvidia-cuda-runtime-cu11==11.8.89
nvidia-cudnn-cu11==********
nvidia-cufft-cu11==*********
nvidia-curand-cu11==*********
nvidia-cusolver-cu11==*********
nvidia-cusparse-cu11==*********
nvidia-nccl-cu11==2.20.5
nvidia-nvtx-cu11==11.8.86
omegaconf==2.3.0
opencv-python==*********
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/packaging_1718189413536/work
pandas==2.2.2
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1712320355065/work
peft==0.11.1
pexpect @ file:///home/<USER>/feedstock_root/build_artifacts/pexpect_1706113125309/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1602536217715/work
pillow==10.2.0
platformdirs @ file:///home/<USER>/feedstock_root/build_artifacts/platformdirs_1715777629804/work
prompt_toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1718047967974/work
protobuf==4.25.3
psutil @ file:///opt/conda/conda-bld/psutil_1656431268089/work
ptyprocess @ file:///home/<USER>/feedstock_root/build_artifacts/ptyprocess_1609419310487/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl
pure-eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1642875951954/work
pyarrow==16.1.0
pyarrow-hotfix==0.6
Pygments @ file:///home/<USER>/feedstock_root/build_artifacts/pygments_1714846767233/work
pyparsing==3.1.2
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/python-dateutil_1709299778482/work
pytorch-lightning==2.2.5
pytz==2024.1
PyYAML==6.0.1
pyzmq @ file:///croot/pyzmq_1705605076900/work
regex==2024.5.15
requests==2.32.3
safetensors==0.4.3
scikit-image==0.23.2
scipy==1.13.1
sentry-sdk==2.5.1
setproctitle==1.3.3
shapely==2.0.4
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1620240208055/work
smmap==5.0.1
stack-data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1669632077133/work
sympy==1.12
tabulate==0.9.0
tensorboard==2.17.0
tensorboard-data-server==0.7.2
tifffile==2024.5.22
tokenizers==0.19.1
torch==2.3.0+cu118
torchaudio==2.3.1+cu118
torchmetrics==1.4.0.post0
torchvision==0.18.1+cu118
tornado @ file:///home/<USER>/feedstock_root/build_artifacts/tornado_1648827254365/work
tqdm==4.66.4
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1713535121073/work
transformers==4.41.2
triton==2.3.0
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/typing_extensions_1717802530399/work
tzdata==2024.1
urllib3==2.2.1
wandb==0.17.1
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1704731205417/work
Werkzeug==3.0.3
xformers==0.0.26.post1+cu118
xxhash==3.4.1
yarl==1.9.4
zipp==3.19.2
