import pandas as pd
import argparse
import os

"""
一个保存了数据集条目信息的csv文件内容如下
scene_name,camera_name,frame_id,included_in_public_release,exclude_reason,split_partition_name
ai_001_004,cam_00,0,True,,train
ai_001_004,cam_00,1,True,,train
ai_001_004,cam_00,2,True,,train
ai_001_004,cam_00,3,True,,train
...
此代码统计此csv中split_partition_name的类别及其分别对应类别的条目数量
"""

def count_split_partitions(csv_path):
    """
    Read a CSV file containing dataset entry information and count entries by split partition.
    
    Args:
        csv_path (str): Path to the CSV file
        
    Returns:
        pd.Series: Count of entries for each split partition
    """
    # Read the CSV file
    df = pd.read_csv(csv_path)
    
    # Count entries by split_partition_name
    counts = df['split_partition_name'].value_counts()
    
    # Calculate percentages
    total = len(df)
    percentages = counts / total * 100
    
    # Print statistics
    print(f"Dataset split statistics from {os.path.basename(csv_path)}:")
    print(f"Total entries: {total}")
    print("\nCounts by partition:")
    for partition, count in counts.items():
        print(f"  {partition}: {count} ({percentages[partition]:.2f}%)")
    
    return counts

def parse_args():
    parser = argparse.ArgumentParser(description="Count entries by split partition in a dataset CSV file")
    parser.add_argument("--csv_path", required=True, help="Path to the CSV file")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    count_split_partitions(args.csv_path)
