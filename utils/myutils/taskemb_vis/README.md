# Task Embedding Visualization Tool

这个工具用于可视化diffusion模型中任务嵌入(task embedding)的生成过程和结果，帮助直观理解不同torch.tensor输入如何转换为最终的嵌入向量。

## 功能特性

### 1. 单任务嵌入可视化
- 显示原始坐标点
- 展示嵌入向量的各个分量（sin(x), sin(y), cos(x), cos(y)）
- 提供嵌入矩阵的热力图
- 显示极坐标表示

### 2. 多任务嵌入比较
- 并排比较多个任务的嵌入
- 计算嵌入之间的余弦相似度
- 显示欧几里得距离矩阵
- 提供PCA降维可视化（如果安装了sklearn）

### 3. 交互式探索
- 可视化预定义任务
- 添加自定义任务坐标
- 选择性比较任务嵌入

## 使用方法

### 基本用法

```python
from taskemb_vis import TaskEmbeddingVisualizer

# 初始化可视化器
visualizer = TaskEmbeddingVisualizer(device='cpu')

# 可视化单个任务嵌入
visualizer.visualize_single_embedding('depth', [1, 0])

# 比较多个任务嵌入
tasks = {
    'depth': [1, 0],
    'normal': [0, 1],
    'rgb': [1, 1]
}
visualizer.compare_embeddings(tasks)
```

### 嵌入计算原理

根据原始代码注释，任务嵌入的计算过程为：

1. **输入张量**: `torch.tensor([x, y]).float().unsqueeze(0)`
2. **三角变换**: `torch.cat([torch.sin(input), torch.cos(input)], dim=-1)`
3. **批次复制**: `.repeat(bsz_per_task, 1)`

最终得到形状为 `[bsz_per_task, 4]` 的嵌入张量。

### 预定义任务类型

工具内置了以下任务类型：

- **depth**: `[1, 0]` - 深度估计任务
- **normal**: `[0, 1]` - 法向量估计任务  
- **rgb**: `[1, 1]` - RGB图像任务
- **segmentation**: `[0, 0]` - 分割任务
- **mixed_1**: `[0.5, 0.5]` - 混合任务1
- **mixed_2**: `[0.8, 0.2]` - 混合任务2

## 安装要求

必需依赖：
```bash
pip install torch numpy matplotlib seaborn
```

可选依赖（用于PCA可视化）：
```bash
pip install scikit-learn
```

## 运行示例

### 直接运行演示
```bash
python taskemb_vis.py
```

### 运行使用示例
```bash
python example_usage.py
```

## 可视化输出说明

### 单任务可视化包含：
1. **原始坐标**: 显示输入的[x, y]坐标点
2. **嵌入分量**: 条形图显示sin(x), sin(y), cos(x), cos(y)的值
3. **嵌入矩阵**: 2x2热力图显示嵌入的结构
4. **极坐标表示**: 显示sin和cos分量的极坐标形式

### 多任务比较包含：
1. **坐标散点图**: 显示所有任务的原始坐标
2. **分量比较**: 并排显示各任务的嵌入分量
3. **相似度矩阵**: 任务间余弦相似度的热力图
4. **嵌入幅度**: 各任务嵌入向量的L2范数
5. **PCA可视化**: 2D降维展示任务关系
6. **距离矩阵**: 任务间欧几里得距离

## 应用场景

1. **调试diffusion模型**: 验证任务嵌入是否符合预期
2. **任务关系分析**: 理解不同任务在嵌入空间中的关系
3. **超参数选择**: 为新任务选择合适的坐标值
4. **模型解释**: 可视化解释模型如何区分不同任务

## 扩展功能

可以通过以下方式扩展工具功能：

1. **添加新的嵌入变换**: 修改`compute_embedding`方法
2. **自定义可视化**: 扩展可视化方法
3. **批量分析**: 处理多批次嵌入数据
4. **导出功能**: 保存可视化结果和数据
