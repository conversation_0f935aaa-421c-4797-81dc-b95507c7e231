#!/usr/bin/env python3
"""
Task Embedding Visualization Tool - Example Usage

This script demonstrates how to use the TaskEmbeddingVisualizer to analyze
task embeddings in diffusion models.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from taskemb_vis import TaskEmbeddingVisualizer
import torch


def main():
    """
    Example usage of the Task Embedding Visualization tool.
    """
    print("Task Embedding Visualization Tool - Example Usage")
    print("=" * 50)
    
    # Initialize the visualizer
    visualizer = TaskEmbeddingVisualizer(device='cpu')
    
    # Example 1: Visualize the embeddings mentioned in the original comment
    print("\n1. Visualizing original comment examples:")
    print("   - depth: [1, 0]")
    print("   - normal: [0, 1]") 
    print("   - rgb: [1, 1]")
    
    # Show individual embeddings
    visualizer.visualize_single_embedding('depth', [1, 0])
    visualizer.visualize_single_embedding('normal', [0, 1])
    visualizer.visualize_single_embedding('rgb', [1, 1])
    
    # Example 2: Compare all three embeddings
    print("\n2. Comparing all three task embeddings:")
    tasks_to_compare = {
        'depth': [1, 0],
        'normal': [0, 1],
        'rgb': [1, 1]
    }
    visualizer.compare_embeddings(tasks_to_compare)
    
    # Example 3: Show how embeddings are computed step by step
    print("\n3. Step-by-step embedding computation:")
    
    input_coords = [1, 0]  # depth task
    print(f"Input coordinates: {input_coords}")
    
    # Step 1: Create tensor
    input_tensor = torch.tensor(input_coords).float().unsqueeze(0)
    print(f"Input tensor: {input_tensor}")
    
    # Step 2: Apply sin/cos transformation
    sin_part = torch.sin(input_tensor)
    cos_part = torch.cos(input_tensor)
    print(f"Sin part: {sin_part}")
    print(f"Cos part: {cos_part}")
    
    # Step 3: Concatenate
    embedding = torch.cat([sin_part, cos_part], dim=-1)
    print(f"Final embedding: {embedding}")
    print(f"Embedding shape: {embedding.shape}")
    
    # Example 4: Interactive mode
    print("\n4. Starting interactive exploration...")
    response = input("Would you like to explore embeddings interactively? (y/n): ")
    if response.lower().startswith('y'):
        visualizer.interactive_exploration()
    
    print("\nExample usage completed!")


if __name__ == "__main__":
    main()
