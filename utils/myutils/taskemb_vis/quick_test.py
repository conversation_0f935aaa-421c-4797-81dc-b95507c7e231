#!/usr/bin/env python3
"""
快速测试动态维度功能
"""

import torch
import numpy as np
import matplotlib.pyplot as plt

def quick_test():
    """快速测试不同维度的嵌入计算"""
    
    print("Quick Dynamic Dimension Test")
    print("=" * 30)
    
    # 测试不同维度的输入
    test_cases = {
        '2D_depth': [1, 0],
        '2D_normal': [0, 1],
        '3D_depth': [1, 0, 1],
        '3D_normal': [0, 1, 0],
        '4D_custom': [1, 0, 1, 0],
        '5D_mixed': [1, 0.5, 0, 0.8, 0.2]
    }
    
    embeddings = {}
    
    print("Computing embeddings...")
    for name, coords in test_cases.items():
        # 计算嵌入
        input_tensor = torch.tensor(coords).float().unsqueeze(0)
        embedding = torch.cat([torch.sin(input_tensor), torch.cos(input_tensor)], dim=-1)
        emb_np = embedding[0].detach().cpu().numpy()
        embeddings[name] = emb_np
        
        print(f"{name:>12}: {len(coords)}D input -> {len(emb_np)}D embedding")
        print(f"             Input: {coords}")
        print(f"             Embedding: {emb_np}")
        print()
    
    # 简单可视化
    plt.figure(figsize=(14, 8))
    
    colors = plt.cm.tab10(np.linspace(0, 1, len(embeddings)))
    
    for i, (name, emb) in enumerate(embeddings.items()):
        dimensions = list(range(len(emb)))
        plt.plot(dimensions, emb, 'o-', label=name, 
                linewidth=2, markersize=6, color=colors[i])
        
        # 标注数值
        for j, val in enumerate(emb):
            plt.annotate(f'{val:.2f}', (j, val), 
                       textcoords="offset points", xytext=(0,8), ha='center', fontsize=8)
    
    plt.xlabel('Dimension Index')
    plt.ylabel('Embedding Value')
    plt.title('Dynamic Dimension Task Embeddings')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()
    
    # 统计信息
    print("Statistics:")
    input_dims = [len(coords) for coords in test_cases.values()]
    emb_dims = [len(emb) for emb in embeddings.values()]
    
    print(f"Input dimensions: {min(input_dims)} - {max(input_dims)}")
    print(f"Embedding dimensions: {min(emb_dims)} - {max(emb_dims)}")
    print(f"Embedding = 2 × Input dimensions: {all(e == 2*i for i, e in zip(input_dims, emb_dims))}")

if __name__ == "__main__":
    quick_test()
