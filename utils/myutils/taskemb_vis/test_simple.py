#!/usr/bin/env python3
"""
简单测试脚本 - 专注于维度数值比较
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import torch
import numpy as np
import matplotlib.pyplot as plt


def simple_embedding_test():
    """
    简单测试嵌入计算和可视化 - 支持动态维度
    """
    print("Task Embedding Dimensions Test (Dynamic Dimensions)")
    print("=" * 50)
    
    # 定义任务 - 支持不同维度
    tasks = {
        'depth_2d': [1, 0],
        'normal_2d': [0, 1], 
        'rgb_2d': [1, 1],
        'depth_3d': [1, 0, 1],
        'normal_3d': [0, 1, 0],
        'rgb_3d': [1, 1, 1],
        'custom_4d': [1, 0, 1, 0]
    }
    
    # 手动计算嵌入
    embeddings = {}
    max_input_dims = 0
    print("Manual embedding calculation:")
    print("-" * 40)
    
    for task_name, coords in tasks.items():
        # 按照原始代码的计算方式，但支持任意维度
        input_tensor = torch.tensor(coords).float().unsqueeze(0)
        embedding = torch.cat([torch.sin(input_tensor), torch.cos(input_tensor)], dim=-1)
        embedding_np = embedding[0].detach().cpu().numpy()
        embeddings[task_name] = embedding_np
        max_input_dims = max(max_input_dims, len(coords))
        
        print(f"{task_name:>12}: input={coords}")
        print(f"             -> embedding_shape={embedding_np.shape}, values={embedding_np}")
        
        # 显示详细的sin/cos计算
        sin_vals = [np.sin(x) for x in coords]
        cos_vals = [np.cos(x) for x in coords]
        print(f"             -> sin values: {sin_vals}")
        print(f"             -> cos values: {cos_vals}")
        print()
    
    print(f"Maximum input dimensions: {max_input_dims}")
    print(f"Maximum embedding dimensions: {max_input_dims * 2}")
    print()
    
    # 动态可视化
    print("Creating dynamic visualization...")
    
    # 确定最大嵌入维度
    max_emb_dims = max(len(emb) for emb in embeddings.values())
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    fig.suptitle(f'Task Embedding Dimensions Comparison (Max dims: {max_emb_dims})', fontsize=14)
    
    # 左图：折线图
    dimensions = list(range(max_emb_dims))
    
    # 动态生成维度标签
    dim_labels = []
    for i in range(max_input_dims):
        dim_labels.extend([f'sin(x{i})', f'cos(x{i})'])
    
    # 如果有不同长度的嵌入，补齐标签
    while len(dim_labels) < max_emb_dims:
        dim_labels.append(f'dim_{len(dim_labels)}')
    
    colors = plt.cm.tab10(np.linspace(0, 1, len(embeddings)))
    
    for i, (task_name, emb) in enumerate(embeddings.items()):
        # 为不同长度的嵌入补零或截断到相同长度
        emb_padded = np.zeros(max_emb_dims)
        emb_padded[:len(emb)] = emb
        
        ax1.plot(dimensions[:len(emb)], emb, 'o-', label=task_name, 
                linewidth=2, markersize=6, color=colors[i])
        
        # 标注数值
        for j, val in enumerate(emb):
            ax1.annotate(f'{val:.2f}', (j, val), 
                       textcoords="offset points", xytext=(0,8), ha='center', fontsize=8)
    
    ax1.set_xlabel('Dimension Index')
    ax1.set_ylabel('Value')
    ax1.set_title('Embedding Values by Dimension')
    ax1.set_xticks(dimensions)
    if len(dim_labels) <= 20:  # 只有在标签不太多时才显示
        ax1.set_xticklabels(dim_labels[:max_emb_dims], rotation=45, ha='right')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 右图：条形图 - 仅显示前几个任务以避免拥挤
    display_tasks = list(embeddings.items())[:5]  # 最多显示5个任务
    
    x = np.arange(max_emb_dims)
    width = 0.8 / len(display_tasks) if display_tasks else 0.8
    
    for i, (task_name, emb) in enumerate(display_tasks):
        offset = (i - len(display_tasks)/2 + 0.5) * width
        
        # 为条形图创建数据
        bar_data = np.zeros(max_emb_dims)
        bar_data[:len(emb)] = emb
        
        bars = ax2.bar(x + offset, bar_data, width, label=task_name, 
                      alpha=0.8, color=colors[i])
        
        # 标注数值 (只标注非零值)
        for j, (bar, val) in enumerate(zip(bars, bar_data)):
            if abs(val) > 1e-6:  # 只标注非零值
                height = bar.get_height()
                ax2.annotate(f'{val:.2f}', 
                           xy=(bar.get_x() + bar.get_width() / 2, height),
                           xytext=(0, 3), textcoords="offset points",
                           ha='center', va='bottom', fontsize=7)
    
    ax2.set_xlabel('Dimension')
    ax2.set_ylabel('Value') 
    ax2.set_title(f'Embedding Values (Bar Chart - Top {len(display_tasks)} tasks)')
    ax2.set_xticks(x)
    if len(dim_labels) <= 20:
        ax2.set_xticklabels(dim_labels[:max_emb_dims], rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 动态数值表格
    print("\nEmbedding Dimensions Table (Dynamic):")
    print("=" * 80)
    
    # 创建表头
    header = f"{'Task':<15}"
    for i in range(max_input_dims):
        header += f" {'sin(x'+str(i)+')' if i < max_input_dims else 'sin(x'+str(i)+')':<10}"
        header += f" {'cos(x'+str(i)+')' if i < max_input_dims else 'cos(x'+str(i)+')':<10}"
    print(header)
    print("-" * len(header))
    
    for task_name, emb in embeddings.items():
        row = f"{task_name:<15}"
        for i in range(max_emb_dims):
            if i < len(emb):
                row += f" {emb[i]:<10.3f}"
            else:
                row += f" {0.0:<10.3f}"
        print(row)
    
    print(f"\nSummary:")
    print(f"- Number of tasks: {len(embeddings)}")
    print(f"- Input dimension range: {min(len(coords) for coords in tasks.values())} - {max(len(coords) for coords in tasks.values())}")
    print(f"- Embedding dimension range: {min(len(emb) for emb in embeddings.values())} - {max(len(emb) for emb in embeddings.values())}")


def custom_dimension_test():
    """
    允许用户自定义维度进行测试
    """
    print("\nCustom Dimension Test")
    print("=" * 25)
    
    tasks = {}
    
    while True:
        task_name = input("Enter task name (or 'done' to finish): ").strip()
        if task_name.lower() == 'done':
            break
            
        try:
            coords_str = input(f"Enter coordinates for {task_name} (comma-separated, e.g., 1,0,1): ").strip()
            coords = [float(x.strip()) for x in coords_str.split(',')]
            tasks[task_name] = coords
            print(f"Added {task_name}: {coords}")
        except ValueError:
            print("Invalid input! Please enter numbers separated by commas.")
    
    if not tasks:
        print("No tasks defined, using default tasks.")
        return simple_embedding_test()
    
    print(f"\nProcessing {len(tasks)} custom tasks...")
    
    # 计算嵌入
    embeddings = {}
    for task_name, coords in tasks.items():
        input_tensor = torch.tensor(coords).float().unsqueeze(0)
        embedding = torch.cat([torch.sin(input_tensor), torch.cos(input_tensor)], dim=-1)
        embedding_np = embedding[0].detach().cpu().numpy()
        embeddings[task_name] = embedding_np
        print(f"{task_name}: {coords} -> {embedding_np}")
    
    # 可视化
    max_emb_dims = max(len(emb) for emb in embeddings.values())
    
    plt.figure(figsize=(12, 6))
    
    colors = plt.cm.tab10(np.linspace(0, 1, len(embeddings)))
    for i, (task_name, emb) in enumerate(embeddings.items()):
        dimensions = list(range(len(emb)))
        plt.plot(dimensions, emb, 'o-', label=task_name, 
                linewidth=2, markersize=6, color=colors[i])
        
        # 标注数值
        for j, val in enumerate(emb):
            plt.annotate(f'{val:.2f}', (j, val), 
                       textcoords="offset points", xytext=(0,8), ha='center')
    
    plt.xlabel('Dimension Index')
    plt.ylabel('Value')
    plt.title('Custom Task Embedding Dimensions')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def test_with_visualizer():
    """
    使用完整的可视化工具测试
    """
    try:
        from taskemb_vis import TaskEmbeddingVisualizer
        
        print("\nTesting with TaskEmbeddingVisualizer:")
        print("=" * 35)
        
        visualizer = TaskEmbeddingVisualizer()
        
        # 快速比较
        print("Running quick_compare()...")
        visualizer.quick_compare()
        
        print("Test completed successfully!")
        
    except ImportError as e:
        print(f"Could not import TaskEmbeddingVisualizer: {e}")
        print("Running simple test instead...")
        simple_embedding_test()


if __name__ == "__main__":
    print("选择测试方式:")
    print("1. 简单测试 (预定义多维度任务)")
    print("2. 完整工具测试")
    print("3. 自定义维度测试")
    
    choice = input("选择 (1, 2, or 3): ").strip()
    
    if choice == "1":
        simple_embedding_test()
    elif choice == "3":
        custom_dimension_test()
    else:
        test_with_visualizer()
