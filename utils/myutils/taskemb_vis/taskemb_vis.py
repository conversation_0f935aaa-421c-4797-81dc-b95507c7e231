"""
Task Embedding Visualization Tool

This tool provides intuitive visualization for different torch.tensor inputs 
and their resulting embeddings in diffusion model class_label scenarios.

Example usage from diffusion model:
task_emb_depth = torch.tensor([1, 0]).float().unsqueeze(0).to(accelerator.device)
task_emb_depth = torch.cat([torch.sin(task_emb_depth), torch.cos(task_emb_depth)], dim=-1).repeat(bsz_per_task, 1)

task_emb_normal = torch.tensor([0, 1]).float().unsqueeze(0).to(accelerator.device)
task_emb_normal = torch.cat([torch.sin(task_emb_normal), torch.cos(task_emb_normal)], dim=-1).repeat(bsz_per_task, 1)

task_emb_rgb = torch.tensor([1, 1]).float().unsqueeze(0).to(accelerator.device)
task_emb_rgb = torch.cat([torch.sin(task_emb_rgb), torch.cos(task_emb_rgb)], dim=-1).repeat(bsz_per_task, 1)
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import warnings

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class TaskEmbeddingVisualizer:
    """
    Task embedding visualization tool for diffusion models.
    Provides intuitive visualization of different task embeddings and their relationships.
    """
    
    def __init__(self, device: str = 'cpu'):
        self.device = device
        self.predefined_tasks = {
            'depth': [1, 0],
            'normal': [0, 1], 
            'rgb': [1, 1],
            'segmentation': [0, 0],
            'mixed_1': [0.5, 0.5],
            'mixed_2': [0.8, 0.2],
            # 3D 示例
            'depth_3d': [1, 0, 1],
            'normal_3d': [0, 1, 0],
            'rgb_3d': [1, 1, 1],
            # 4D 示例
            'custom_4d': [1, 0, 1, 0],
            'mixed_4d': [0.5, 0.5, 0.8, 0.2]
        }
    
    def compute_embedding(self, input_tensor: torch.Tensor, bsz_per_task: int = 1) -> torch.Tensor:
        """
        Compute task embedding using sin/cos transformation.
        支持任意维度的输入。
        
        Args:
            input_tensor: Input tensor representing task coordinates (any length)
            bsz_per_task: Batch size per task
            
        Returns:
            Embedding tensor of shape [bsz_per_task, input_dims*2]
        """
        if not isinstance(input_tensor, torch.Tensor):
            input_tensor = torch.tensor(input_tensor).float()
        
        # 确保是2D张量 [1, input_dims]
        if input_tensor.dim() == 1:
            input_tensor = input_tensor.unsqueeze(0)
        
        input_tensor = input_tensor.float().to(self.device)
        embedding = torch.cat([torch.sin(input_tensor), torch.cos(input_tensor)], dim=-1)
        return embedding.repeat(bsz_per_task, 1)
    
    def visualize_single_embedding(self, task_name: str, input_coords: List[float], 
                                 bsz_per_task: int = 1, save_path: Optional[str] = None):
        """
        Visualize a single task embedding.
        
        Args:
            task_name: Name of the task
            input_coords: Input coordinates [x, y]
            bsz_per_task: Batch size per task
            save_path: Path to save the plot
        """
        embedding = self.compute_embedding(input_coords, bsz_per_task)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'Task Embedding Visualization: {task_name}', fontsize=16)
        
        # Original coordinates
        axes[0, 0].scatter(input_coords[0], input_coords[1], c='red', s=100, alpha=0.8)
        axes[0, 0].set_xlim(-1.5, 1.5)
        axes[0, 0].set_ylim(-1.5, 1.5)
        axes[0, 0].set_title('Original Coordinates')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].axhline(y=0, color='k', linestyle='-', alpha=0.3)
        axes[0, 0].axvline(x=0, color='k', linestyle='-', alpha=0.3)
        
        # Embedding components
        emb_np = embedding[0].detach().cpu().numpy()
        components = ['sin(x)', 'sin(y)', 'cos(x)', 'cos(y)']
        colors = ['blue', 'green', 'orange', 'purple']
        
        axes[0, 1].bar(components, emb_np, color=colors, alpha=0.7)
        axes[0, 1].set_title('Embedding Components')
        axes[0, 1].set_ylabel('Value')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Embedding heatmap
        emb_reshaped = emb_np.reshape(2, 2)
        sns.heatmap(emb_reshaped, annot=True, fmt='.3f', cmap='coolwarm', 
                   center=0, ax=axes[1, 0], xticklabels=['sin', 'cos'], 
                   yticklabels=['x', 'y'])
        axes[1, 0].set_title('Embedding Matrix (2x2)')
        
        # Polar representation
        theta = np.linspace(0, 2*np.pi, 100)
        r_sin = np.abs(emb_np[:2])  # sin components
        r_cos = np.abs(emb_np[2:])  # cos components
        
        axes[1, 1] = plt.subplot(2, 2, 4, projection='polar')
        axes[1, 1].plot([0, input_coords[0]], [0, np.linalg.norm(r_sin)], 'b-', linewidth=3, label='sin components')
        axes[1, 1].plot([np.pi/2, input_coords[1] + np.pi/2], [0, np.linalg.norm(r_cos)], 'r-', linewidth=3, label='cos components')
        axes[1, 1].set_title('Polar Representation')
        axes[1, 1].legend()
        
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_embeddings(self, tasks: Dict[str, List[float]], save_path: Optional[str] = None):
        """
        Compare multiple task embeddings.
        
        Args:
            tasks: Dictionary of task_name -> input_coordinates
            save_path: Path to save the plot
        """
        embeddings = {}
        for task_name, coords in tasks.items():
            embeddings[task_name] = self.compute_embedding(coords)[0].detach().cpu().numpy()
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Task Embeddings Comparison', fontsize=16)
        
        # Original coordinates scatter plot
        for task_name, coords in tasks.items():
            axes[0, 0].scatter(coords[0], coords[1], label=task_name, s=100, alpha=0.8)
        axes[0, 0].set_xlim(-1.5, 1.5)
        axes[0, 0].set_ylim(-1.5, 1.5)
        axes[0, 0].set_title('Original Coordinates')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Embedding components comparison
        components = ['sin(x)', 'sin(y)', 'cos(x)', 'cos(y)']
        x = np.arange(len(components))
        width = 0.8 / len(tasks)
        
        for i, (task_name, emb) in enumerate(embeddings.items()):
            axes[0, 1].bar(x + i * width, emb, width, label=task_name, alpha=0.7)
        axes[0, 1].set_xlabel('Components')
        axes[0, 1].set_ylabel('Value')
        axes[0, 1].set_title('Embedding Components Comparison')
        axes[0, 1].set_xticks(x + width * (len(tasks) - 1) / 2)
        axes[0, 1].set_xticklabels(components)
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Similarity heatmap
        similarity_matrix = np.zeros((len(tasks), len(tasks)))
        task_names = list(tasks.keys())
        
        for i, task1 in enumerate(task_names):
            for j, task2 in enumerate(task_names):
                emb1 = embeddings[task1]
                emb2 = embeddings[task2]
                similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
                similarity_matrix[i, j] = similarity
        
        sns.heatmap(similarity_matrix, annot=True, fmt='.3f', cmap='coolwarm',
                   xticklabels=task_names, yticklabels=task_names, ax=axes[0, 2])
        axes[0, 2].set_title('Cosine Similarity Matrix')
        
        # Embedding magnitude comparison
        magnitudes = [np.linalg.norm(emb) for emb in embeddings.values()]
        axes[1, 0].bar(task_names, magnitudes, alpha=0.7, color='skyblue')
        axes[1, 0].set_title('Embedding Magnitudes')
        axes[1, 0].set_ylabel('L2 Norm')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # PCA visualization
        if len(tasks) >= 2:
            try:
                from sklearn.decomposition import PCA
                emb_matrix = np.array(list(embeddings.values()))
                if emb_matrix.shape[1] > 2:
                    pca = PCA(n_components=2)
                    emb_2d = pca.fit_transform(emb_matrix)
                    
                    for i, task_name in enumerate(task_names):
                        axes[1, 1].scatter(emb_2d[i, 0], emb_2d[i, 1], label=task_name, s=100)
                        axes[1, 1].annotate(task_name, (emb_2d[i, 0], emb_2d[i, 1]), 
                                          xytext=(5, 5), textcoords='offset points')
                    axes[1, 1].set_title('PCA Visualization (2D)')
                    axes[1, 1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
                    axes[1, 1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
                    axes[1, 1].grid(True, alpha=0.3)
            except ImportError:
                # Fallback to simple 2D projection if sklearn is not available
                emb_matrix = np.array(list(embeddings.values()))
                for i, task_name in enumerate(task_names):
                    axes[1, 1].scatter(emb_matrix[i, 0], emb_matrix[i, 2], label=task_name, s=100)
                    axes[1, 1].annotate(task_name, (emb_matrix[i, 0], emb_matrix[i, 2]), 
                                      xytext=(5, 5), textcoords='offset points')
                axes[1, 1].set_title('Embedding Projection (sin_x vs cos_x)')
                axes[1, 1].set_xlabel('sin(x)')
                axes[1, 1].set_ylabel('cos(x)')
                axes[1, 1].grid(True, alpha=0.3)
        
        # Distance matrix
        distance_matrix = np.zeros((len(tasks), len(tasks)))
        for i, task1 in enumerate(task_names):
            for j, task2 in enumerate(task_names):
                dist = np.linalg.norm(embeddings[task1] - embeddings[task2])
                distance_matrix[i, j] = dist
        
        sns.heatmap(distance_matrix, annot=True, fmt='.3f', cmap='viridis',
                   xticklabels=task_names, yticklabels=task_names, ax=axes[1, 2])
        axes[1, 2].set_title('Euclidean Distance Matrix')
        
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def compare_embedding_dimensions(self, tasks: Dict[str, List[float]], 
                                   bsz_per_task: int = 1, save_path: Optional[str] = None):
        """
        Compare embedding dimensions across different tasks.
        横轴是维度索引，纵轴是该维度上的数值标量。支持动态维度。
        
        Args:
            tasks: Dictionary of task_name -> input_coordinates (支持任意长度)
            bsz_per_task: Batch size per task
            save_path: Path to save the plot
        """
        embeddings = {}
        max_input_dims = 0
        max_emb_dims = 0
        
        for task_name, coords in tasks.items():
            embedding = self.compute_embedding(coords, bsz_per_task)
            emb_np = embedding[0].detach().cpu().numpy()
            embeddings[task_name] = emb_np
            max_input_dims = max(max_input_dims, len(coords))
            max_emb_dims = max(max_emb_dims, len(emb_np))
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle(f'Task Embedding Dimensions Comparison (Max: {max_emb_dims}D)', fontsize=16)
        
        # 动态生成维度标签
        dimension_labels = []
        for i in range(max_input_dims):
            dimension_labels.extend([f'sin(x{i})', f'cos(x{i})'])
        
        # 左图：折线图比较各维度数值
        colors = plt.cm.Set1(np.linspace(0, 1, len(tasks)))
        
        for i, (task_name, emb) in enumerate(embeddings.items()):
            dimensions = list(range(len(emb)))
            ax1.plot(dimensions, emb, 'o-', label=task_name, 
                    linewidth=2, markersize=6, color=colors[i])
            
            # 在每个点上标注数值
            for j, val in enumerate(emb):
                ax1.annotate(f'{val:.2f}', (j, val), 
                           textcoords="offset points", xytext=(0,8), ha='center', fontsize=8)
        
        ax1.set_xlabel('Dimension Index')
        ax1.set_ylabel('Value')
        ax1.set_title('Embedding Values by Dimension')
        ax1.set_xticks(range(max_emb_dims))
        if max_emb_dims <= 20:  # 只有在标签不太多时才显示
            ax1.set_xticklabels(dimension_labels[:max_emb_dims], rotation=45, ha='right')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 右图：条形图比较 (只显示前5个任务以避免拥挤)
        display_tasks = list(embeddings.items())[:5]
        x = np.arange(max_emb_dims)
        width = 0.8 / len(display_tasks) if display_tasks else 0.8
        
        for i, (task_name, emb) in enumerate(display_tasks):
            # 为不同长度的嵌入创建对齐的数据
            bar_data = np.zeros(max_emb_dims)
            bar_data[:len(emb)] = emb
            
            offset = (i - len(display_tasks)/2 + 0.5) * width
            bars = ax2.bar(x + offset, bar_data, width, label=task_name, 
                          alpha=0.8, color=colors[i])
            
            # 在每个条形上标注数值 (只标注非零值)
            for j, (bar, val) in enumerate(zip(bars, bar_data)):
                if abs(val) > 1e-6:
                    height = bar.get_height()
                    ax2.annotate(f'{val:.2f}', 
                               xy=(bar.get_x() + bar.get_width() / 2, height),
                               xytext=(0, 3), textcoords="offset points",
                               ha='center', va='bottom', fontsize=8)
        
        ax2.set_xlabel('Dimension')
        ax2.set_ylabel('Value')
        ax2.set_title(f'Embedding Values (Top {len(display_tasks)} tasks)')
        ax2.set_xticks(x)
        if max_emb_dims <= 20:
            ax2.set_xticklabels(dimension_labels[:max_emb_dims], rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印动态数值表格
        print(f"\nEmbedding Dimensions Table (Dynamic - Max {max_emb_dims}D):")
        print("=" * (15 + max_emb_dims * 12))
        
        # 创建表头
        header = f"{'Task':<15}"
        for i, label in enumerate(dimension_labels[:max_emb_dims]):
            header += f" {label:<10}"
        print(header)
        print("-" * len(header))
        
        # 打印数据行
        for task_name, emb in embeddings.items():
            row = f"{task_name:<15}"
            for i in range(max_emb_dims):
                if i < len(emb):
                    row += f" {emb[i]:<10.3f}"
                else:
                    row += f" {0.0:<10.3f}"
            print(row)
        
        print(f"\nSummary:")
        print(f"- Number of tasks: {len(embeddings)}")
        print(f"- Input dimension range: {min(len(coords) for coords in tasks.values())} - {max(len(coords) for coords in tasks.values())}")
        print(f"- Embedding dimension range: {min(len(emb) for emb in embeddings.values())} - {max(len(emb) for emb in embeddings.values())}")
    
    def quick_compare(self, task_names: List[str] = None):
        """
        快速比较预定义任务的嵌入维度。
        
        Args:
            task_names: 要比较的任务名称列表，如果为None则比较所有预定义任务
        """
        if task_names is None:
            # 使用原始注释中提到的三个主要任务
            tasks_to_compare = {
                'depth': [1, 0],
                'normal': [0, 1],
                'rgb': [1, 1]
            }
        else:
            tasks_to_compare = {name: self.predefined_tasks[name] 
                              for name in task_names if name in self.predefined_tasks}
        
        print("Quick Embedding Comparison")
        print("=" * 30)
        print("Input coordinates:")
        for name, coords in tasks_to_compare.items():
            print(f"  {name}: {coords}")
        print()
        
        self.compare_embedding_dimensions(tasks_to_compare)

    def interactive_exploration(self):
        """
        Interactive exploration of task embeddings.
        """
        print("Task Embedding Interactive Exploration")
        print("=" * 40)
        
        while True:
            print("\nOptions:")
            print("1. Visualize predefined tasks")
            print("2. Add custom task")
            print("3. Compare selected tasks (full comparison)")
            print("4. Compare embedding dimensions (simple)")
            print("5. Quick compare (depth, normal, rgb)")
            print("6. Quit")
            
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == '1':
                print("\nPredefined tasks:")
                for i, (name, coords) in enumerate(self.predefined_tasks.items(), 1):
                    print(f"{i}. {name}: {coords}")
                
                task_idx = input("Select task number: ").strip()
                try:
                    task_name = list(self.predefined_tasks.keys())[int(task_idx) - 1]
                    coords = self.predefined_tasks[task_name]
                    self.visualize_single_embedding(task_name, coords)
                except (ValueError, IndexError):
                    print("Invalid selection!")
            
            elif choice == '2':
                task_name = input("Enter task name: ").strip()
                try:
                    x = float(input("Enter x coordinate: "))
                    y = float(input("Enter y coordinate: "))
                    self.predefined_tasks[task_name] = [x, y]
                    print(f"Added task '{task_name}' with coordinates [{x}, {y}]")
                except ValueError:
                    print("Invalid coordinates!")
            
            elif choice == '3':
                print("\nAvailable tasks:")
                for i, name in enumerate(self.predefined_tasks.keys(), 1):
                    print(f"{i}. {name}")
                
                selected = input("Enter task numbers to compare (comma-separated): ").strip()
                try:
                    indices = [int(x.strip()) - 1 for x in selected.split(',')]
                    task_names = list(self.predefined_tasks.keys())
                    selected_tasks = {task_names[i]: self.predefined_tasks[task_names[i]] 
                                    for i in indices}
                    self.compare_embeddings(selected_tasks)
                except (ValueError, IndexError):
                    print("Invalid selection!")
            
            elif choice == '4':
                print("\nAvailable tasks:")
                for i, name in enumerate(self.predefined_tasks.keys(), 1):
                    print(f"{i}. {name}")
                
                selected = input("Enter task numbers to compare dimensions (comma-separated): ").strip()
                try:
                    indices = [int(x.strip()) - 1 for x in selected.split(',')]
                    task_names = list(self.predefined_tasks.keys())
                    selected_tasks = {task_names[i]: self.predefined_tasks[task_names[i]] 
                                    for i in indices}
                    self.compare_embedding_dimensions(selected_tasks)
                except (ValueError, IndexError):
                    print("Invalid selection!")
            
            elif choice == '5':
                self.quick_compare()
            
            elif choice == '6':
                print("Goodbye!")
                break
            
            else:
                print("Invalid choice!")


def demo():
    """
    Demonstration of the task embedding visualization tool.
    """
    print("Task Embedding Visualization Demo")
    print("=" * 35)
    
    visualizer = TaskEmbeddingVisualizer()
    
    # Demo 1: 快速比较嵌入维度 (主要功能)
    print("\n1. Quick Embedding Dimensions Comparison")
    print("Comparing depth, normal, and rgb embeddings...")
    visualizer.quick_compare()
    
    # Demo 2: 自定义任务比较
    print("\n2. Custom Tasks Dimensions Comparison")
    custom_tasks = {
        'depth': [1, 0],
        'normal': [0, 1],
        'rgb': [1, 1],
        'segmentation': [0, 0],
        'mixed': [0.5, 0.5]
    }
    visualizer.compare_embedding_dimensions(custom_tasks)
    
    # Demo 3: 单个嵌入详细可视化
    print("\n3. Single Task Embedding Visualization")
    response = input("Would you like to see detailed visualization for 'depth' task? (y/n): ")
    if response.lower().startswith('y'):
        visualizer.visualize_single_embedding('depth', [1, 0])
    
    # Demo 4: 完整比较
    print("\n4. Full Embeddings Comparison")
    response = input("Would you like to see full comparison analysis? (y/n): ")
    if response.lower().startswith('y'):
        tasks_to_compare = {
            'depth': [1, 0],
            'normal': [0, 1],
            'rgb': [1, 1],
            'segmentation': [0, 0]
        }
        visualizer.compare_embeddings(tasks_to_compare)
    
    # Demo 5: 交互式探索
    print("\n5. Interactive Exploration")
    response = input("Would you like to start interactive exploration? (y/n): ")
    if response.lower().startswith('y'):
        visualizer.interactive_exploration()


if __name__ == "__main__":
    demo()