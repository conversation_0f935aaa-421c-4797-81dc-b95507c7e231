import torch
import math
import matplotlib.pyplot as plt
import numpy as np
from ipywidgets import interact, IntSlider

'''
为了理解时间步的嵌入，实现一个可视化工具
qwen3
'''

def get_timestep_embedding(timesteps, embedding_dim):
    """
    将时间步转换为高维嵌入向量。
    
    :param timesteps: 时间步张量 (batch_size,)
    :param embedding_dim: 嵌入维度
    :return: 时间步嵌入 (batch_size, embedding_dim)
    """
    half_dim = embedding_dim // 2
    # 创建频率值
    freqs = torch.exp(
        -math.log(10000) * torch.arange(start=0, end=half_dim, dtype=torch.float32) / half_dim
    ).to(device=timesteps.device)
    # 计算时间步的嵌入
    args = timesteps[:, None].float() * freqs[None]
    embedding = torch.cat([torch.sin(args), torch.cos(args)], dim=-1)
    if embedding_dim % 2:
        embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)
    return embedding

def plot_embeddings(timestep, embedding_dim=128):
    """
    绘制给定时间步的正弦、余弦以及拼接后的嵌入曲线。
    
    :param timestep: 输入的时间步
    :param embedding_dim: 嵌入维度
    """
    timesteps = torch.tensor([timestep])
    embeddings = get_timestep_embedding(timesteps, embedding_dim)[0]

    half_dim = embedding_dim // 2
    freqs = torch.exp(
        -math.log(10000) * torch.arange(start=0, end=half_dim, dtype=torch.float32) / half_dim
    ).to(device=timesteps.device)

    # 计算正弦和余弦值
    args = timestep * freqs
    sin_values = torch.sin(args).cpu().numpy()
    cos_values = torch.cos(args).cpu().numpy()

    # 绘制图表
    fig, axs = plt.subplots(3, figsize=(10, 12))
    
    # 正弦曲线
    axs[0].plot(np.arange(half_dim), sin_values, label='sin', color='blue')
    axs[0].set_title(f'Sine Embedding for Timestep {timestep}')
    axs[0].set_xlabel('Frequency Index')
    axs[0].set_ylabel('Value')
    axs[0].legend()

    # 余弦曲线
    axs[1].plot(np.arange(half_dim), cos_values, label='cos', color='red')
    axs[1].set_title(f'Cosine Embedding for Timestep {timestep}')
    axs[1].set_xlabel('Frequency Index')
    axs[1].set_ylabel('Value')
    axs[1].legend()

    # 拼接后的嵌入曲线
    full_embedding = embeddings.cpu().numpy()
    axs[2].plot(np.arange(embedding_dim), full_embedding, color='green')
    axs[2].set_title(f'Concatenated Embedding for Timestep {timestep}')
    axs[2].set_xlabel('Embedding Dimension')
    axs[2].set_ylabel('Value')

    plt.tight_layout()
    plt.show()

# 创建交互式控件
@interact(timestep=IntSlider(min=0, max=1000, step=1, value=500),
          embedding_dim=IntSlider(min=4, max=256, step=4, value=128))
def interactive_plot(timestep, embedding_dim):
    plot_embeddings(timestep, embedding_dim)