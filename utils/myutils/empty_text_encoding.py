import torch
import os

def load_empty_prompt_embeddings(embeddings_path, device=None):
    """
    Load pre-generated empty prompt embeddings from a file.

    Args:
        embeddings_path (str): Path to the saved embeddings file
        device (torch.device, optional): Device to load the embeddings to

    Returns:
        torch.Tensor: The loaded embeddings
    """
    if not os.path.exists(embeddings_path):
        raise FileNotFoundError(f"Embeddings file not found at {embeddings_path}")

    embeddings = torch.load(embeddings_path, map_location=device if device else torch.device('cpu'))
    print(f"Loaded empty prompt embeddings from {embeddings_path}")
    print(f"Shape: {embeddings.shape}")

    return embeddings

def get_or_generate_empty_prompt_embeddings(model_path, embeddings_path, device=None, force_regenerate=False):
    """
    Get empty prompt embeddings by loading from a file.

    Args:
        model_path (str): Path to the model (not used, kept for backward compatibility)
        embeddings_path (str): Path to load the embeddings
        device (torch.device, optional): Device to load the embeddings to
        force_regenerate (bool): Not used, kept for backward compatibility

    Returns:
        torch.Tensor: The empty prompt embeddings
    """
    return load_empty_prompt_embeddings(embeddings_path, device)