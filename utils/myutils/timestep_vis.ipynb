{"cells": [{"cell_type": "code", "execution_count": null, "id": "a912b178-b3e5-41b1-aa4c-e1f4adc9340b", "metadata": {}, "outputs": [], "source": ["import torch\n", "import math\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "from ipywidgets import interact, IntSlider\n", "\n", "'''\n", "为了理解时间步的嵌入，实现一个可视化工具\n", "qwen3\n", "'''\n", "\n", "def get_timestep_embedding(timesteps, embedding_dim):\n", "    \"\"\"\n", "    将时间步转换为高维嵌入向量。\n", "    \n", "    :param timesteps: 时间步张量 (batch_size,)\n", "    :param embedding_dim: 嵌入维度\n", "    :return: 时间步嵌入 (batch_size, embedding_dim)\n", "    \"\"\"\n", "    half_dim = embedding_dim // 2\n", "    # 创建频率值\n", "    freqs = torch.exp(\n", "        -math.log(10000) * torch.arange(start=0, end=half_dim, dtype=torch.float32) / half_dim\n", "    ).to(device=timesteps.device)\n", "    # 计算时间步的嵌入\n", "    args = timesteps[:, None].float() * freqs[None]\n", "    embedding = torch.cat([torch.sin(args), torch.cos(args)], dim=-1)\n", "    if embedding_dim % 2:\n", "        embedding = torch.cat([embedding, torch.zeros_like(embedding[:, :1])], dim=-1)\n", "    return embedding\n", "\n", "def plot_embeddings(timestep, embedding_dim=128):\n", "    \"\"\"\n", "    绘制给定时间步的正弦、余弦以及拼接后的嵌入曲线。\n", "    \n", "    :param timestep: 输入的时间步\n", "    :param embedding_dim: 嵌入维度\n", "    \"\"\"\n", "    timesteps = torch.tensor([timestep])\n", "    embeddings = get_timestep_embedding(timesteps, embedding_dim)[0]\n", "\n", "    half_dim = embedding_dim // 2\n", "    freqs = torch.exp(\n", "        -math.log(10000) * torch.arange(start=0, end=half_dim, dtype=torch.float32) / half_dim\n", "    ).to(device=timesteps.device)\n", "\n", "    # 计算正弦和余弦值\n", "    args = timestep * freqs\n", "    sin_values = torch.sin(args).cpu().numpy()\n", "    cos_values = torch.cos(args).cpu().numpy()\n", "\n", "    # 绘制图表\n", "    fig, axs = plt.subplots(3, figsize=(5, 6))\n", "    \n", "    # 正弦曲线\n", "    axs[0].plot(np.arange(half_dim), sin_values, label='sin', color='blue')\n", "    axs[0].set_title(f'Sine Embedding for Timestep {timestep}')\n", "    axs[0].set_xlabel('Frequency Index')\n", "    axs[0].set_ylabel('Value')\n", "    axs[0].legend()\n", "\n", "    # 余弦曲线\n", "    axs[1].plot(np.arange(half_dim), cos_values, label='cos', color='red')\n", "    axs[1].set_title(f'Cosine Embedding for Timestep {timestep}')\n", "    axs[1].set_xlabel('Frequency Index')\n", "    axs[1].set_ylabel('Value')\n", "    axs[1].legend()\n", "\n", "    # 拼接后的嵌入曲线\n", "    full_embedding = embeddings.cpu().numpy()\n", "    axs[2].plot(np.arange(embedding_dim), full_embedding, color='green')\n", "    axs[2].set_title(f'Concatenated Embedding for Timestep {timestep}')\n", "    axs[2].set_xlabel('Embedding Dimension')\n", "    axs[2].set_ylabel('Value')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 创建交互式控件\n", "@interact(timestep=IntSlider(min=0, max=1000, step=1, value=500),\n", "          embedding_dim=IntSlider(min=4, max=256, step=4, value=128))\n", "def interactive_plot(timestep, embedding_dim):\n", "    plot_embeddings(timestep, embedding_dim)"]}, {"cell_type": "code", "execution_count": null, "id": "605d9282-1c06-478c-8946-b152c8dccbb4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "diffusion", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}