"""
vkitti数据集保存在datasets/vkitti/路径中，其下三个文件夹分别保存了三种数据depth, rgb, textgt，
每种数据集文件夹下的子路径结构是几乎相同的:
datasets
└── vkitti
    ├── depth
    │   ├── Scene01
    │   ├── Scene02
    │   ├── Scene06
    │   ├── Scene18
    │   └── Scene20
    ├── rgb
    │   ├── Scene01
    │   ├── Scene02
    │   ├── Scene06
    │   ├── Scene18
    │   └── Scene20
    └── textgt
        ├── Scene01
        ├── Scene02
        ├── Scene06
        ├── Scene18
        └── Scene20

现在我想让将三种数据合并，尽可能地共用相同的路径结构如下：
$PATH_TO_VKITTI_DATA/
SceneX/Y/frames/rgb/Camera_Z/rgb_%05d.jpg
SceneX/Y/frames/depth/Camera_Z/depth_%05d.png
SceneX/Y/colors.txt
SceneX/Y/extrinsic.txt
SceneX/Y/intrinsic.txt
SceneX/Y/info.txt
SceneX/Y/bbox.txt
SceneX/Y/pose.txt

【250714】实现
【250715】最终没有使用，而是直接在Marigold中将3个压缩包同时解压，期望相同路径的数据会自动合并
"""

import os
import shutil
import glob
import argparse
from tqdm import tqdm
import re

def relocate_vkitti_data(src_path, dst_path):
    """
    Reorganize vkitti dataset from original structure to the new structure.
    
    Args:
        src_path (str): Source path to the original vkitti dataset
        dst_path (str): Destination path for the reorganized dataset
    """
    # Create destination directory if it doesn't exist
    os.makedirs(dst_path, exist_ok=True)
    
    # Get all scene folders
    scenes = ["Scene01", "Scene02", "Scene06", "Scene18", "Scene20"]
    
    # Define variations/conditions in vkitti
    variations = ["15-deg-left", "15-deg-right", "30-deg-left", "30-deg-right","clone", "fog", "morning", "overcast", "rain", "sunset"]
    cameras = ["Camera_0", "Camera_1"]
    
    # Regular expression to extract frame number from filenames
    frame_pattern = re.compile(r'.*_(\d+)\..*')
    
    for scene in scenes:
        print(f"Processing {scene}...")
        
        # For each scene, process all variations
        for variation in variations:
            # Check if this variation exists for this scene
            rgb_var_path = os.path.join(src_path, "rgb", scene, variation)
            if not os.path.exists(rgb_var_path):
                continue
                
            print(f"  Processing variation: {variation}")
            
            # Create destination structure
            scene_var_path = os.path.join(dst_path, scene, variation)
            os.makedirs(scene_var_path, exist_ok=True)
            
            # Create frames directory structure
            for camera in cameras:
                # Create rgb directory
                rgb_dst_dir = os.path.join(scene_var_path, "frames", "rgb", camera)
                os.makedirs(rgb_dst_dir, exist_ok=True)
                
                # Create depth directory
                depth_dst_dir = os.path.join(scene_var_path, "frames", "depth", camera)
                os.makedirs(depth_dst_dir, exist_ok=True)
            
            # move RGB images
            for camera in cameras:
                rgb_src_dir = os.path.join(src_path, "rgb", scene, variation, camera)
                if not os.path.exists(rgb_src_dir):
                    continue
                    
                rgb_files = glob.glob(os.path.join(rgb_src_dir, "*.jpg"))
                for rgb_file in tqdm(rgb_files, desc=f"  Copying RGB for {camera}"):
                    # Extract frame number
                    match = frame_pattern.match(rgb_file)
                    if match:
                        frame_num = int(match.group(1))
                        # Create new filename
                        new_filename = f"rgb_{frame_num:05d}.jpg"
                        dst_file = os.path.join(scene_var_path, "frames", "rgb", camera, new_filename)
                        # move file
                        shutil.move(rgb_file, dst_file)
                        # shutil.copy2(rgb_file, dst_file)
            
            # move depth images
            for camera in cameras:
                depth_src_dir = os.path.join(src_path, "depth", scene, variation, camera)
                if not os.path.exists(depth_src_dir):
                    continue
                    
                depth_files = glob.glob(os.path.join(depth_src_dir, "*.png"))
                for depth_file in tqdm(depth_files, desc=f"  Copying depth for {camera}"):
                    # Extract frame number
                    match = frame_pattern.match(depth_file)
                    if match:
                        frame_num = int(match.group(1))
                        # Create new filename
                        new_filename = f"depth_{frame_num:05d}.png"
                        dst_file = os.path.join(scene_var_path, "frames", "depth", camera, new_filename)
                        shutil.move(depth_file, dst_file)
                        # shutil.copy2(depth_file, dst_file)
            
            # Copy metadata files if they exist
            metadata_files = {
                "colors.txt": os.path.join(src_path, "textgt", scene, variation, "colors.txt"),
                "extrinsic.txt": os.path.join(src_path, "textgt", scene, variation, "extrinsic.txt"),
                "intrinsic.txt": os.path.join(src_path, "textgt", scene, variation, "intrinsic.txt"),
                "info.txt": os.path.join(src_path, "textgt", scene, variation, "info.txt"),
                "bbox.txt": os.path.join(src_path, "textgt", scene, variation, "bbox.txt"),
                "pose.txt": os.path.join(src_path, "textgt", scene, variation, "pose.txt")
            }
            
            for filename, src_file in metadata_files.items():
                if os.path.exists(src_file):
                    dst_file = os.path.join(scene_var_path, filename)
                    shutil.move(src_file, dst_file)
                    # shutil.copy2(src_file, dst_file)
                    print(f"  Moved {filename}")

def parse_args():
    parser = argparse.ArgumentParser(description="Relocate vkitti dataset to a new structure")
    parser.add_argument("--src_path", required=True, help="Source path to the original vkitti dataset")
    parser.add_argument("--dst_path", required=True, help="Destination path for the reorganized dataset")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    relocate_vkitti_data(args.src_path, args.dst_path)
    print("Dataset reorganization completed!")
