#!/usr/bin/env python3
"""
Test script for depth2normal_D2NT.py
Validates the DAG-based normal generation implementation
"""

import numpy as np
import cv2
from PIL import Image
import os
import sys

# Add current directory to path to import the module
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from depth2normal_D2NT import (
    process_depth_to_normal_DAG,
    normal_to_rgb_lotus,
    get_filter,
    get_DAG_filter,
    vector_normalization
)

def create_test_depth():
    """Create a synthetic depth map for testing"""
    h, w = 100, 100
    depth = np.ones((h, w), dtype=np.float32) * 5.0  # 5 meters
    
    # Add a step discontinuity
    depth[40:60, 40:60] = 2.0  # 2 meters (closer)
    
    # Add some noise
    noise = np.random.normal(0, 0.1, (h, w))
    depth += noise
    depth = np.clip(depth, 0.1, 10.0)
    
    return depth

def test_basic_functionality():
    """Test basic functionality of the DAG method"""
    print("Testing basic functionality...")
    
    # Create test data
    depth = create_test_depth()
    intrinsics = [500.0, 500.0, 50.0, 50.0]  # fx, fy, cx, cy
    
    # Test different versions
    versions = ['d2nt_basic', 'd2nt_v2', 'd2nt_v3']
    
    for version in versions:
        print(f"  Testing {version}...")
        
        # Process depth to normal
        normal = process_depth_to_normal_DAG(depth, intrinsics, version)
        
        # Check output shape
        assert normal.shape == (depth.shape[0], depth.shape[1], 3), f"Wrong shape for {version}"
        
        # Check value range (should be roughly [-1, 1])
        assert np.all(normal >= -2.0) and np.all(normal <= 2.0), f"Values out of range for {version}"
        
        # Check that vectors are roughly normalized
        norms = np.linalg.norm(normal, axis=2)
        assert np.all(norms > 0.5) and np.all(norms < 1.5), f"Vectors not normalized for {version}"
        
        print(f"    ✓ {version} passed basic tests")

def test_rgb_conversion():
    """Test RGB conversion for Lotus compatibility"""
    print("Testing RGB conversion...")
    
    # Create test normal map
    h, w = 50, 50
    normal = np.random.randn(h, w, 3)
    normal = normal / np.linalg.norm(normal, axis=2, keepdims=True)  # Normalize
    
    # Convert to RGB
    normal_rgb = normal_to_rgb_lotus(normal)
    
    # Check output properties
    assert normal_rgb.shape == (h, w, 3), "Wrong RGB shape"
    assert normal_rgb.dtype == np.uint8, "Wrong RGB dtype"
    assert np.all(normal_rgb >= 0) and np.all(normal_rgb <= 255), "RGB values out of range"
    
    # Test round-trip conversion
    normal_recovered = (normal_rgb.astype(np.float32) / 255.0) * 2.0 - 1.0
    
    # Should be close to original (within quantization error)
    diff = np.abs(normal - normal_recovered)
    max_diff = np.max(diff)
    assert max_diff < 0.02, f"Round-trip error too large: {max_diff}"
    
    print("    ✓ RGB conversion passed")

def test_filter_functions():
    """Test the filter functions"""
    print("Testing filter functions...")
    
    # Create test depth map
    depth = create_test_depth() * 100  # Convert to cm for filters
    
    # Test basic filter
    Gu_basic, Gv_basic = get_filter(depth)
    assert Gu_basic.shape == depth.shape, "Basic filter Gu wrong shape"
    assert Gv_basic.shape == depth.shape, "Basic filter Gv wrong shape"
    
    # Test DAG filter
    Gu_dag, Gv_dag = get_DAG_filter(depth)
    assert Gu_dag.shape == depth.shape, "DAG filter Gu wrong shape"
    assert Gv_dag.shape == depth.shape, "DAG filter Gv wrong shape"
    
    # DAG should produce different results than basic
    assert not np.allclose(Gu_basic, Gu_dag), "DAG and basic filters too similar"
    assert not np.allclose(Gv_basic, Gv_dag), "DAG and basic filters too similar"
    
    print("    ✓ Filter functions passed")

def test_edge_preservation():
    """Test that DAG method preserves edges better"""
    print("Testing edge preservation...")
    
    # Create depth map with sharp edge
    h, w = 100, 100
    depth = np.ones((h, w), dtype=np.float32) * 5.0
    depth[:, 50:] = 2.0  # Sharp vertical edge
    
    intrinsics = [500.0, 500.0, 50.0, 50.0]
    
    # Process with basic and DAG methods
    normal_basic = process_depth_to_normal_DAG(depth, intrinsics, 'd2nt_basic')
    normal_dag = process_depth_to_normal_DAG(depth, intrinsics, 'd2nt_v3')
    
    # Check that both produce valid normals
    assert normal_basic.shape == (h, w, 3), "Basic normal wrong shape"
    assert normal_dag.shape == (h, w, 3), "DAG normal wrong shape"
    
    # Both should have different characteristics near the edge
    edge_region = normal_basic[:, 45:55, :]
    assert np.std(edge_region) > 0.1, "No variation near edge in basic method"
    
    edge_region_dag = normal_dag[:, 45:55, :]
    assert np.std(edge_region_dag) > 0.1, "No variation near edge in DAG method"
    
    print("    ✓ Edge preservation test passed")

def test_lotus_compatibility():
    """Test compatibility with Lotus format expectations"""
    print("Testing Lotus compatibility...")
    
    # Create test normal map
    depth = create_test_depth()
    intrinsics = [500.0, 500.0, 50.0, 50.0]
    
    # Generate normal map
    normal = process_depth_to_normal_DAG(depth, intrinsics, 'd2nt_v3')
    normal_rgb = normal_to_rgb_lotus(normal)
    
    # Create PIL Image (as done in the main script)
    pil_image = Image.fromarray(normal_rgb, "RGB")
    
    # Test that we can save and reload
    test_path = "/tmp/test_normal.png"
    pil_image.save(test_path)
    
    # Reload and verify
    reloaded = Image.open(test_path).convert("RGB")
    reloaded_array = np.array(reloaded)
    
    assert np.array_equal(normal_rgb, reloaded_array), "Save/load mismatch"
    
    # Test Lotus preprocessing simulation
    # This simulates what happens in the Lotus data loader
    normal_tensor = reloaded_array.astype(np.float32) / 255.0  # [0, 1]
    normal_tensor = normal_tensor * 2.0 - 1.0  # [-1, 1] (Lotus preprocessing)
    
    # Should be close to original normal map
    normal_transposed = normal.transpose(2, 0, 1)  # (3, H, W)
    normal_tensor_transposed = normal_tensor.transpose(2, 0, 1)  # (3, H, W)
    
    # Check that the preprocessing gives reasonable results
    assert normal_tensor_transposed.shape == normal_transposed.shape, "Shape mismatch after preprocessing"
    
    # Clean up
    if os.path.exists(test_path):
        os.remove(test_path)
    
    print("    ✓ Lotus compatibility test passed")

def run_all_tests():
    """Run all tests"""
    print("Running DAG-based depth2normal tests...\n")
    
    try:
        test_basic_functionality()
        test_rgb_conversion()
        test_filter_functions()
        test_edge_preservation()
        test_lotus_compatibility()
        
        print("\n✅ All tests passed! The DAG implementation is working correctly.")
        print("\nThe generated normal maps should be fully compatible with Lotus training.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
