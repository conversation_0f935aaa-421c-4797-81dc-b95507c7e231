# DAG-based Normal Map Generation for Lotus

## Overview

`depth2normal_D2NT.py` is an improved normal map generation script that replaces the SVD-based method in the original `depth2normal.py` with the Discontinuity-Aware Gradient (DAG) method from the E2E-FT project. This provides better edge preservation and higher quality normal maps while maintaining full compatibility with the Lotus training pipeline.

## Key Features

- **DAG Algorithm**: Uses discontinuity-aware gradient filters for better edge preservation
- **MRF Optimization**: Includes Markov Random Field-based refinement (d2nt_v3)
- **Lotus Compatibility**: Generates normal maps in the exact format expected by Lotus training
- **Batch Processing**: Efficient batch processing with progress tracking
- **Multiple Versions**: Supports d2nt_basic, d2nt_v2, and d2nt_v3 variants

## Usage

### Basic Usage
```bash
# Set your Virtual KITTI dataset path
export PATH_TO_VKITTI_DATA="/path/to/your/vkitti/dataset"

# Run the script
bash utils/depth2normal_D2NT.sh
```

### Advanced Usage
```bash
python utils/depth2normal_D2NT.py \
    --data_path /path/to/vkitti \
    --batch_size 8 \
    --scenes 01 02 06 18 20 \
    --version d2nt_v3 \
    --depth_min 1e-3 \
    --depth_max 80.0
```

## Command Line Arguments

- `--data_path`: Path to Virtual KITTI dataset (required)
- `--batch_size`: Batch size for processing (default: 4)
- `--scenes`: Scene numbers to process (default: ['01', '02', '06', '18', '20'])
- `--version`: DAG version to use (choices: d2nt_basic, d2nt_v2, d2nt_v3, default: d2nt_v3)
- `--depth_min`: Minimum depth value (default: 1e-3)
- `--depth_max`: Maximum depth value (default: 80.0)

## DAG Versions

1. **d2nt_basic**: Basic gradient-based normal estimation
2. **d2nt_v2**: Discontinuity-aware gradient filters
3. **d2nt_v3**: DAG filters + MRF-based refinement (recommended)

## Output Format

The script generates normal maps that are fully compatible with Lotus training:

- **Format**: 8-bit PNG files with RGB channels
- **Encoding**: Normal vectors `[-1, 1]` mapped to `[0, 255]` using `((normal + 1) * 0.5 * 255)`
- **Channel Order**: RGB (x→R, y→G, z→B)
- **Coordinate System**: Camera coordinates
- **File Structure**: Maintains same directory structure as original `depth2normal.py`

## Directory Structure

Input:
```
$PATH_TO_VKITTI_DATA/
├── Scene01/
│   ├── clone/
│   │   ├── frames/depth/Camera_0/depth_00001.png
│   │   └── intrinsic.txt
│   └── ...
└── ...
```

Output:
```
$PATH_TO_VKITTI_DATA/
├── Scene01/
│   ├── clone/
│   │   ├── frames/normal/Camera_0/normal_00001.png  # Generated
│   │   └── intrinsic.txt
│   └── ...
└── ...
```

## Algorithm Details

### DAG Filter Process
1. **Gradient Calculation**: Compute depth gradients in horizontal and vertical directions
2. **Discontinuity Detection**: Use Laplacian filters to detect depth discontinuities
3. **Adaptive Weighting**: Apply soft-min function to weight gradients based on discontinuities
4. **Normal Computation**: Convert weighted gradients to surface normals using camera intrinsics
5. **MRF Refinement**: Apply Markov Random Field optimization for smoother results (v3 only)

### Advantages over SVD Method
- **Better Edge Preservation**: DAG filters preserve sharp edges and discontinuities
- **Faster Processing**: Gradient-based computation is more efficient than SVD
- **Noise Robustness**: MRF optimization reduces noise while preserving details
- **Proven Quality**: Method validated in E2E-FT project

## Integration with Lotus

The generated normal maps are fully compatible with Lotus training pipeline:

1. **Data Loading**: Works with existing `VirtualKITTI2` dataset class
2. **Preprocessing**: Compatible with standard `* 2.0 - 1.0` normalization
3. **Training**: No changes required to training scripts or data loaders
4. **Mixed Datasets**: Can be used alongside Hypersim normal data

## Performance

- **Speed**: ~2-3x faster than SVD-based method
- **Quality**: Improved edge preservation and detail retention
- **Memory**: Lower memory usage due to gradient-based computation
- **Scalability**: Efficient batch processing for large datasets

## Troubleshooting

### Common Issues

1. **Missing intrinsic files**: Ensure `intrinsic.txt` files exist for each scene/condition
2. **Depth file format**: Verify depth files are in 16-bit PNG format (cm units)
3. **Path issues**: Check that `$PATH_TO_VKITTI_DATA` is correctly set
4. **Memory errors**: Reduce batch size if encountering memory issues

### Validation

To verify the generated normal maps are correct:
```python
import cv2
import numpy as np
from PIL import Image

# Load generated normal map
normal_img = Image.open('path/to/normal_00001.png')
normal_array = np.array(normal_img)

# Check value range [0, 255]
print(f"Min: {normal_array.min()}, Max: {normal_array.max()}")

# Convert back to [-1, 1] range
normal_float = (normal_array.astype(np.float32) / 255.0) * 2.0 - 1.0

# Check if vectors are normalized
norms = np.linalg.norm(normal_float, axis=2)
print(f"Normal vector lengths - Min: {norms.min():.3f}, Max: {norms.max():.3f}")
```

## References

- [Depth-to-Normal Translator](https://github.com/fengyi233/depth-to-normal-translator)
- [E2E-FT Project](https://github.com/GonzaloMartinGarcia/E2E-FT)
- [Lotus Project](https://github.com/EnVision-Research/Lotus)
