#!/bin/bash
# export PYTHONPATH="$(dirname "$(dirname "$0")"):$PYTHONPATH"
# Multi-Task Learning (MTL) training script with Hypersim + VKITTI datasets
# This script trains the deterministic Lotus-D MTL model using both Hypersim and VKITTI datasets
# 【250820】Deterministic paradigm MTL training script adapted from generative version. 直接上传集群同步调试

export MODEL_NAME="stabilityai/stable-diffusion-2-base"

# training datasets - Hypersim and VKITTI (both contain RGB, depth, and normal data)
export TRAIN_DATA_DIR_HYPERSIM="/mnt/data2/zhoushuo/Lotus/datasets/hypersim/processed"
export TRAIN_DATA_DIR_VKITTI="/mnt/data2/zhoushuo/Lotus/datasets/vkitti"
export RES_HYPERSIM=576
export RES_VKITTI=512
export P_HYPERSIM=0.9  # Probability of using Hypersim vs VKITTI (90% Hypersim, 10% VKITTI)
export NORMTYPE="trunc_disparity"
export TRUNCNORM_MIN=0.02  # Minimum value for truncated normal distribution

# training configs
export BATCH_SIZE=8
export CUDA=012
export GAS=8
export TOTAL_BSZ=$(($BATCH_SIZE * ${#CUDA} * $GAS))

# MTL model configs - task-specific timesteps
export TIMESTEP_RGB=599      # Timestep for RGB reconstruction task
export TIMESTEP_DEPTH=799    # Timestep for depth estimation task
export TIMESTEP_NORMAL=999   # Timestep for normal estimation task
export TASK_NAME="mtl"       # Multi-task learning identifier

# MTL task frequency ratios - Balanced approach
export TASK_RATIO_RGB=1.0    # RGB reconstruction frequency ratio
export TASK_RATIO_DEPTH=1.0  # Depth estimation frequency ratio
export TASK_RATIO_NORMAL=1.0 # Normal estimation frequency ratio
# Expected distribution: RGB ~33%, Depth ~33%, Normal ~33%

# MTL loss weights - Balanced approach
export LOSS_WEIGHT_RGB=1.0    # RGB loss weight (gradient contribution)
export LOSS_WEIGHT_DEPTH=1.0  # Depth loss weight (gradient contribution)
export LOSS_WEIGHT_NORMAL=1.0 # Normal loss weight (gradient contribution)

# eval
export BASE_TEST_DATA_DIR="datasets/eval/"
export VALIDATION_IMAGES="datasets/quick_validation/"
export VAL_STEP=500

# output dir
export OUTPUT_DIR="output/train-lotus-d-MTL-bsz${TOTAL_BSZ}-tr${TIMESTEP_RGB}-td${TIMESTEP_DEPTH}-tn${TIMESTEP_NORMAL}-balanced/"

accelerate launch --config_file=accelerate_configs/$CUDA.yaml --mixed_precision="fp16" \
  --main_process_port="13224" \
  train_lotus_d-MTL_dev.py \
  --pretrained_model_name_or_path=$MODEL_NAME \
  --train_data_dir_hypersim=$TRAIN_DATA_DIR_HYPERSIM \
  --resolution_hypersim=$RES_HYPERSIM \
  --train_data_dir_vkitti=$TRAIN_DATA_DIR_VKITTI \
  --resolution_vkitti=$RES_VKITTI \
  --prob_hypersim=$P_HYPERSIM \
  --mix_dataset \
  --random_flip \
  --align_cam_normal \
  --norm_type=$NORMTYPE \
  --truncnorm_min=$TRUNCNORM_MIN \
  --dataloader_num_workers=0 \
  --train_batch_size=$BATCH_SIZE \
  --gradient_accumulation_steps=$GAS \
  --gradient_checkpointing \
  --max_grad_norm=1 \
  --seed=42 \
  --max_train_steps=10000 \
  --learning_rate=3e-05 \
  --lr_scheduler="constant" --lr_warmup_steps=0 \
  --task_name=$TASK_NAME \
  --timestep_rgb=$TIMESTEP_RGB \
  --timestep_depth=$TIMESTEP_DEPTH \
  --timestep_normal=$TIMESTEP_NORMAL \
  --task_ratio_rgb=$TASK_RATIO_RGB \
  --task_ratio_depth=$TASK_RATIO_DEPTH \
  --task_ratio_normal=$TASK_RATIO_NORMAL \
  --loss_weight_rgb=$LOSS_WEIGHT_RGB \
  --loss_weight_depth=$LOSS_WEIGHT_DEPTH \
  --loss_weight_normal=$LOSS_WEIGHT_NORMAL \
  --validation_images=$VALIDATION_IMAGES \
  --validation_steps=$VAL_STEP \
  --checkpointing_steps=$VAL_STEP \
  --checkpoints_total_limit=5 \
  --base_test_data_dir=$BASE_TEST_DATA_DIR \
  --output_dir=$OUTPUT_DIR \
  --empty_text_embed_path="utils/empty_text_embeddings.pt" \
  --resume_from_checkpoint="latest" \
  --FULL_EVALUATION
