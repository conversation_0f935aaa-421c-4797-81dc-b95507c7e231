# export PYTHONPATH="$(dirname "$(dirname "$0")"):$PYTHONPATH"
# Multi-Task Learning (MTL) training script for Lotus-G with timestep-based task conditioning
# This script trains a unified model for RGB reconstruction, depth estimation, and normal estimation

export MODEL_NAME="stabilityai/stable-diffusion-2-base"

# training dataset - only Hypersim (contains RGB, depth, and normal data)
export TRAIN_DATA_DIR_HYPERSIM="/home/<USER>/Lotus-main/datasets/hypersim/processed"
export RES_HYPERSIM=576
export NORMTYPE="trunc_disparity"

# training configs
export BATCH_SIZE=1
export CUDA=0
export GAS=2
export TOTAL_BSZ=$(($BATCH_SIZE * ${#CUDA} * $GAS))

# MTL model configs - task-specific timesteps (optimized based on task difficulty)
export TIMESTEP_RGB=600      # RGB reconstruction (easiest) → lower timestep (less noise)
export TIMESTEP_DEPTH=800    # Depth estimation (medium) → medium timestep
export TIMESTEP_NORMAL=999   # Normal estimation (hardest) → higher timestep (more noise)
export TASK_NAME="mtl"       # Multi-task learning identifier

# MTL task frequency ratios (optional - defaults to 1.0:1.0:1.0 for equal distribution)
# Uncomment and modify the following lines to control task training frequency:
# export TASK_RATIO_RGB=1.0    # RGB reconstruction frequency ratio
# export TASK_RATIO_DEPTH=2.0  # Depth estimation frequency ratio (2x more than others)
# export TASK_RATIO_NORMAL=1.0 # Normal estimation frequency ratio

# MTL loss weights (optional - defaults to 1.0:1.0:1.0 for equal gradient contribution)
# Uncomment and modify the following lines to control loss contribution to gradients:
# export LOSS_WEIGHT_RGB=1.0    # RGB loss weight (gradient contribution)
# export LOSS_WEIGHT_DEPTH=3.0  # Depth loss weight (3x gradient contribution)
# export LOSS_WEIGHT_NORMAL=1.0 # Normal loss weight (gradient contribution)

# eval
export BASE_TEST_DATA_DIR="datasets/eval/"
export VALIDATION_IMAGES="datasets/quick_validation/"
export VAL_STEP=5

# output dir
export OUTPUT_DIR="output/train-lotus-g-MTL-hypersim-bsz${TOTAL_BSZ}-tr${TIMESTEP_RGB}-td${TIMESTEP_DEPTH}-tn${TIMESTEP_NORMAL}/"

accelerate launch --config_file=accelerate_configs/$CUDA.yaml --mixed_precision="fp16" \
  --main_process_port="13224" \
  train_lotus_g-MTL.py \
  --pretrained_model_name_or_path=$MODEL_NAME \
  --train_data_dir_hypersim=$TRAIN_DATA_DIR_HYPERSIM \
  --resolution_hypersim=$RES_HYPERSIM \
  --random_flip \
  --norm_type=$NORMTYPE \
  --dataloader_num_workers=0 \
  --train_batch_size=$BATCH_SIZE \
  --gradient_accumulation_steps=$GAS \
  --gradient_checkpointing \
  --max_grad_norm=1 \
  --seed=42 \
  --max_train_steps=20 \
  --learning_rate=3e-05 \
  --lr_scheduler="constant" --lr_warmup_steps=0 \
  --task_name=$TASK_NAME \
  --timestep_rgb=$TIMESTEP_RGB \
  --timestep_depth=$TIMESTEP_DEPTH \
  --timestep_normal=$TIMESTEP_NORMAL \
  ${TASK_RATIO_RGB:+--task_ratio_rgb=$TASK_RATIO_RGB} \
  ${TASK_RATIO_DEPTH:+--task_ratio_depth=$TASK_RATIO_DEPTH} \
  ${TASK_RATIO_NORMAL:+--task_ratio_normal=$TASK_RATIO_NORMAL} \
  ${LOSS_WEIGHT_RGB:+--loss_weight_rgb=$LOSS_WEIGHT_RGB} \
  ${LOSS_WEIGHT_DEPTH:+--loss_weight_depth=$LOSS_WEIGHT_DEPTH} \
  ${LOSS_WEIGHT_NORMAL:+--loss_weight_normal=$LOSS_WEIGHT_NORMAL} \
  --validation_images=$VALIDATION_IMAGES \
  --validation_steps=$VAL_STEP \
  --checkpointing_steps=1000 \
  --checkpoints_total_limit=3 \
  --base_test_data_dir=$BASE_TEST_DATA_DIR \
  --output_dir=$OUTPUT_DIR \
  --empty_text_embed_path="utils/empty_text_embeddings.pt" \
  --resume_from_checkpoint="latest"
  