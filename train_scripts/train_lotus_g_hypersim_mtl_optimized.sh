#!/bin/bash
# export PYTHONPATH="$(dirname "$(dirname "$0")"):$PYTHONPATH"
# Multi-Task Learning (MTL) training script with OPTIMIZED timesteps based on task difficulty analysis
# 
# Task Difficulty Analysis (from RDN training):
# - Normal estimation: loss=0.1 (hardest) → timestep=999 (max noise, max denoising challenge)
# - Depth estimation: loss=0.03 (medium) → timestep=800 (medium noise)  
# - RGB reconstruction: loss=0.00025 (easiest) → timestep=600 (min noise, easier denoising)

export MODEL_NAME="stabilityai/stable-diffusion-2-base"

# training dataset - only Hypersim (contains RGB, depth, and normal data)
export TRAIN_DATA_DIR_HYPERSIM="/home/<USER>/Lotus-main/datasets/hypersim/processed"
export RES_HYPERSIM=576
export NORMTYPE="trunc_disparity"

# training configs
export BATCH_SIZE=1
export CUDA=0
export GAS=2
export TOTAL_BSZ=$(($BATCH_SIZE * ${#CUDA} * $GAS))

# MTL model configs - OPTIMIZED timesteps based on task difficulty
export TIMESTEP_RGB=600      # RGB (easiest, loss=0.00025) → lower timestep (less noise)
export TIMESTEP_DEPTH=800    # Depth (medium, loss=0.03) → medium timestep
export TIMESTEP_NORMAL=999   # Normal (hardest, loss=0.1) → higher timestep (more noise)
export TASK_NAME="mtl"       # Multi-task learning identifier

# MTL task frequency ratios - equal for fair comparison
export TASK_RATIO_RGB=1.0    # Equal frequency
export TASK_RATIO_DEPTH=1.0  # Equal frequency
export TASK_RATIO_NORMAL=1.0 # Equal frequency

# MTL loss weights - equal for fair comparison  
export LOSS_WEIGHT_RGB=1.0    # Equal gradient contribution
export LOSS_WEIGHT_DEPTH=1.0  # Equal gradient contribution
export LOSS_WEIGHT_NORMAL=1.0 # Equal gradient contribution

# eval
export BASE_TEST_DATA_DIR="datasets/eval/"
export VALIDATION_IMAGES="datasets/quick_validation/"
export VAL_STEP=5

# output dir
export OUTPUT_DIR="output/train-lotus-g-MTL-optimized-hypersim-bsz${TOTAL_BSZ}-tr${TIMESTEP_RGB}-td${TIMESTEP_DEPTH}-tn${TIMESTEP_NORMAL}/"

accelerate launch --config_file=accelerate_configs/$CUDA.yaml --mixed_precision="fp16" \
  --main_process_port="13224" \
  train_lotus_g-MTL.py \
  --pretrained_model_name_or_path=$MODEL_NAME \
  --train_data_dir_hypersim=$TRAIN_DATA_DIR_HYPERSIM \
  --resolution_hypersim=$RES_HYPERSIM \
  --random_flip \
  --norm_type=$NORMTYPE \
  --dataloader_num_workers=0 \
  --train_batch_size=$BATCH_SIZE \
  --gradient_accumulation_steps=$GAS \
  --gradient_checkpointing \
  --max_grad_norm=1 \
  --seed=42 \
  --max_train_steps=20 \
  --learning_rate=3e-05 \
  --lr_scheduler="constant" --lr_warmup_steps=0 \
  --task_name=$TASK_NAME \
  --timestep_rgb=$TIMESTEP_RGB \
  --timestep_depth=$TIMESTEP_DEPTH \
  --timestep_normal=$TIMESTEP_NORMAL \
  --task_ratio_rgb=$TASK_RATIO_RGB \
  --task_ratio_depth=$TASK_RATIO_DEPTH \
  --task_ratio_normal=$TASK_RATIO_NORMAL \
  --loss_weight_rgb=$LOSS_WEIGHT_RGB \
  --loss_weight_depth=$LOSS_WEIGHT_DEPTH \
  --loss_weight_normal=$LOSS_WEIGHT_NORMAL \
  --validation_images=$VALIDATION_IMAGES \
  --validation_steps=$VAL_STEP \
  --checkpointing_steps=1000 \
  --checkpoints_total_limit=3 \
  --base_test_data_dir=$BASE_TEST_DATA_DIR \
  --output_dir=$OUTPUT_DIR \
  --empty_text_embed_path="utils/empty_text_embeddings.pt" \
  --resume_from_checkpoint="latest"
