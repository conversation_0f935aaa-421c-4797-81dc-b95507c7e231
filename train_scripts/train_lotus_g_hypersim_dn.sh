# export PYTHONPATH="$(dirname "$(dirname "$0")"):$PYTHONPATH"
# 在改建DN任务之前，跑通normal训练

export MODEL_NAME="stabilityai/stable-diffusion-2-base"

# training dataset - only Hypersim
export TRAIN_DATA_DIR_HYPERSIM="/home/<USER>/Lotus-main/datasets/hypersim/processed"
export RES_HYPERSIM=576
export NORMTYPE="trunc_disparity"

# training configs
export BATCH_SIZE=3
export CUDA=0
export GAS=2
export TOTAL_BSZ=$(($BATCH_SIZE * ${#CUDA} * $GAS))

# model configs
export TIMESTEP=999
export TASK_NAME="dn"

# eval
export BASE_TEST_DATA_DIR="datasets/eval/"
export VALIDATION_IMAGES="datasets/quick_validation/"
export VAL_STEP=2

# output dir
export OUTPUT_DIR="output/train-lotus-g-${TASK_NAME}-hypersim-only-bsz${TOTAL_BSZ}2/"

accelerate launch --config_file=accelerate_configs/$CUDA.yaml --mixed_precision="fp16" \
  --main_process_port="13224" \
  train_lotus_g-DN.py \
  --pretrained_model_name_or_path=$MODEL_NAME \
  --train_data_dir_hypersim=$TRAIN_DATA_DIR_HYPERSIM \
  --resolution_hypersim=$RES_HYPERSIM \
  --random_flip \
  --norm_type=$NORMTYPE \
  --dataloader_num_workers=0 \
  --train_batch_size=$BATCH_SIZE \
  --gradient_accumulation_steps=$GAS \
  --gradient_checkpointing \
  --max_grad_norm=1 \
  --seed=42 \
  --max_train_steps=2 \
  --learning_rate=3e-05 \
  --lr_scheduler="constant" --lr_warmup_steps=0 \
  --task_name=$TASK_NAME \
  --timestep=$TIMESTEP \
  --validation_images=$VALIDATION_IMAGES \
  --validation_steps=$VAL_STEP \
  --checkpointing_steps=500 \
  --checkpoints_total_limit=3 \
  --base_test_data_dir=$BASE_TEST_DATA_DIR \
  --output_dir=$OUTPUT_DIR \
  --empty_text_embed_path="utils/empty_text_embeddings.pt" \
  --resume_from_checkpoint="latest"
  