DSINE SOFTWARE

LICENCE AGREEMENT

WE (Imperial College of Science, Technology and Medicine, (“Imperial College
London”)) ARE WILLING TO LICENSE THIS SOFTWARE TO YOU (a licensee “You”) ONLY
ON THE CONDITION THAT YOU ACCEPT ALL OF THE TERMS CONTAINED IN THE FOLLOWING
AGREEMENT. PLEASE READ THE AGREEMENT CAREFULLY BEFORE DOWNLOADING THE SOFTWARE.
BY EXERCISING THE OPTION TO DOWNLOAD THE SOFTWARE YOU AGREE TO BE BOUND BY THE
TERMS OF THE AGREEMENT.

SOFTWARE LICENCE AGREEMENT (EXCLUDING BSD COMPONENTS)

1. This Agreement pertains to a worldwide, non-exclusive, temporary, fully
paid-up, royalty free, non-transferable, non-sub- licensable licence (the
“Licence”) to use the elastic fusion source code, including any modification,
part or derivative (the “Software”).

Ownership and Licence. Your rights to use and download the Software onto your
computer, and all other copies that You are authorised to make, are specified
in this Agreement. However, we (or our licensors) retain all rights, including
but not limited to all copyright and other intellectual property rights
anywhere in the world, in the Software not expressly granted to You in this
Agreement.

2. Permitted use of the Licence:

(a) You may download and install the Software onto one computer or server for
use in accordance with Clause 2(b) of this Agreement provided that You ensure
that the Software is not accessible by other users unless they have themselves
accepted the terms of this licence agreement.

(b) You may use the Software solely for non-commercial, internal  or academic
research purposes and only in accordance with the terms of this Agreement. You
may not use the Software for commercial purposes, including but not limited to
(1) integration of all or part of the source code or the Software into a
product for sale or licence by or on behalf of You to third parties or (2) use
of the Software or any derivative of it for research to develop software
products for sale or licence to a third party or (3) use of the Software or any
derivative of it for research to develop non-software products for sale or
licence to a third party, or (4) use of the Software to provide any service to
an external organisation for which payment is received.

Should You wish to use the Software for commercial purposes, You shall
email <EMAIL> .

(c) Right to Copy. You may copy the Software for back-up and archival purposes,
provided that each copy is kept in your possession and provided You reproduce
our copyright notice (set out in Schedule 1) on each copy.

(d) Transfer and sub-licensing. You may not rent, lend, or lease the Software
and You may not transmit, transfer or sub-license this licence to use the
Software or any of your rights or obligations under this Agreement to another
party.

(e) Identity of Licensee. The licence granted herein is personal to You. You
shall not permit any third party to access, modify or otherwise use the
Software nor shall You access modify or otherwise use the Software on behalf of
any third party. If You wish to obtain a licence for mutiple users or a site
licence for the Software please contact us
at <EMAIL> .

(f) Publications and presentations. You may make public, results or data
obtained from, dependent on or arising from research carried out using the
Software, provided that any such presentation or publication identifies the
Software as the source of the results or the data, including the Copyright
Notice given in each element of the Software, and stating that the Software has
been made available for use by You under licence from Imperial College London
and You provide a copy of any such publication to Imperial College London.

3. Prohibited Uses. You may not, without written permission from us
at <EMAIL> :

(a) Use, copy, modify, merge, or transfer copies of the Software or any
documentation provided by us which relates to the Software except as provided
in this Agreement;

(b) Use any back-up or archival copies of the Software (or allow anyone else to
use such copies) for any purpose other than to replace the original copy in the
event it is destroyed or becomes defective; or

(c) Disassemble, decompile or "unlock", reverse translate, or in any manner
decode the Software for any reason.

4. Warranty Disclaimer

(a) Disclaimer. The Software has been developed for research purposes only. You
acknowledge that we are providing the Software to You under this licence
agreement free of charge and on condition that the disclaimer set out below
shall apply. We do not represent or warrant that the Software as to: (i) the
quality, accuracy or reliability of the Software; (ii) the suitability of the
Software for any particular use or for use under any specific conditions; and
(iii) whether use of the Software will infringe third-party rights.

You acknowledge that You have reviewed and evaluated the Software to determine
that it meets your needs and that You assume all responsibility and liability
for determining the suitability of the Software as fit for your particular
purposes and requirements. Subject to Clause 4(b), we exclude and expressly
disclaim all express and implied representations, warranties, conditions and
terms not stated herein (including the implied conditions or warranties of
satisfactory quality, merchantable quality, merchantability and fitness for
purpose).

(b) Savings. Some jurisdictions may imply warranties, conditions or terms or
impose obligations upon us which cannot, in whole or in part, be excluded,
restricted or modified or otherwise do not allow the exclusion of implied
warranties, conditions or terms, in which case the above warranty disclaimer
and exclusion will only apply to You to the extent permitted in the relevant
jurisdiction and does not in any event exclude any implied warranties,
conditions or terms which may not under applicable law be excluded.

(c) Imperial College London disclaims all responsibility for the use which is
made of the Software and any liability for the outcomes arising from using the
Software.

5. Limitation of Liability

(a) You acknowledge that we are providing the Software to You under this
licence agreement free of charge and on condition that the limitation of
liability set out below shall apply. Accordingly, subject to Clause 5(b), we
exclude all liability whether in contract, tort, negligence or otherwise, in
respect of the Software and/or any related documentation provided to You by us
including, but not limited to, liability for loss or corruption of data, loss
of contracts, loss of income, loss of profits, loss of cover and any
consequential or indirect loss or damage of any kind arising out of or in
connection with this licence agreement, however caused. This exclusion shall
apply even if we have been advised of the possibility of such loss or damage.

(b) You agree to indemnify Imperial College London and hold it harmless from
and against any and all claims, damages and liabilities asserted by third
parties (including claims for negligence) which arise directly or indirectly
from the use of the Software or any derivative of it or the sale of any
products based on the Software. You undertake to make no liability claim
against any employee, student, agent or appointee of Imperial College London,
in connection with this Licence or the Software.

(c) Nothing in this Agreement shall have the effect of excluding or limiting
our statutory liability.

(d) Some jurisdictions do not allow these limitations or exclusions either
wholly or in part, and, to that extent, they may not apply to you. Nothing in
this licence agreement will affect your statutory rights or other relevant
statutory provisions which cannot be excluded, restricted or modified, and its
terms and conditions must be read and construed subject to any such statutory
rights and/or provisions.

6. Confidentiality. You agree not to disclose any confidential information
provided to You by us pursuant to this Agreement to any third party without our
prior written consent. The obligations in this Clause 6 shall survive the
termination of this Agreement for any reason.

7. Termination.

(a) We may terminate this licence agreement and your right to use the Software
at any time with immediate effect upon written notice to You.

(b) This licence agreement and your right to use the Software automatically
terminate if You:

  (i) fail to comply with any provisions of this Agreement; or

  (ii) destroy the copies of the Software in your possession, or voluntarily
  return the Software to us.

(c) Upon termination You will destroy all copies of the Software.

(d) Otherwise, the restrictions on your rights to use the Software will expire
10 (ten) years after first use of the Software under this licence agreement.

8. Miscellaneous Provisions.

(a) This Agreement will be governed by and construed in accordance with the
substantive laws of England and Wales whose courts shall have exclusive
jurisdiction over all disputes which may arise between us.

(b) This is the entire agreement between us relating to the Software, and
supersedes any prior purchase order, communications, advertising or
representations concerning the Software.

(c) No change or modification of this Agreement will be valid unless it is in
writing, and is signed by us.

(d) The unenforceability or invalidity of any part of this Agreement will not
affect the enforceability or validity of the remaining parts.

BSD Elements of the Software

For BSD elements of the Software, the following terms shall apply:
Copyright as indicated in the header of the individual element of the Software.
All rights reserved.

Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are met:

1. Redistributions of source code must retain the above copyright notice, this
list of conditions and the following disclaimer.

2. Redistributions in binary form must reproduce the above copyright notice,
this list of conditions and the following disclaimer in the documentation
and/or other materials provided with the distribution.

3. Neither the name of the copyright holder nor the names of its contributors
may be used to endorse or promote products derived from this software without
specific prior written permission.

THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 
SCHEDULE 1

The Software

DSINE is a framework for estimating surface normals from a single image. It is based on the techniques described in the following publication:

    • Gwangbin Bae, Andrew J. Davison. Rethinking Inductive Biases for Surface Normal Estimation. CVPR, 2024
_________________________

Acknowledgments

If you use the software, you should reference the following paper in any publication:
 
    • Gwangbin Bae, Andrew J. Davison. Rethinking Inductive Biases for Surface Normal Estimation. CVPR, 2024