#!/usr/bin/env python3
"""
Validation script for MTL inference functionality
This script checks if the infer_MTL.py has the correct pipeline calls and arguments
"""

import ast
import sys
from pathlib import Path

def validate_mtl_inference():
    """Validate the MTL inference script for correct implementation"""
    
    script_path = Path("infer_MTL.py")
    if not script_path.exists():
        print("❌ infer_MTL.py not found!")
        return False
    
    with open(script_path, 'r') as f:
        content = f.read()
    
    # Parse the AST to analyze the code
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"❌ Syntax error in infer_MTL.py: {e}")
        return False
    
    print("✅ infer_MTL.py syntax is valid")
    
    # Check for key features
    checks = {
        "timestep_depth argument": "--timestep_depth" in content,
        "timestep_normal argument": "--timestep_normal" in content,
        "MTL task support": 'task_name == "mtl"' in content,
        "Depth-only support": 'task_name == "depth"' in content,
        "Normal-only support": 'task_name == "normal"' in content,
        "Separate output directories": "output_dir_depth_color" in content,
        "Pipeline calls without task_emb": "task_emb=" not in content.replace("# task_emb", ""),
        "Timestep-based conditioning": "timesteps=[args.timestep_depth]" in content,
    }
    
    print("\n📋 Feature Validation:")
    all_passed = True
    for feature, passed in checks.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {feature}")
        if not passed:
            all_passed = False
    
    # Check for potential issues
    issues = []
    
    # Check if old task_emb usage is removed
    if "task_emb=" in content and "# task_emb" not in content:
        issues.append("Found task_emb usage - should be removed for MTL models")
    
    # Check if timesteps are used correctly
    if "timesteps=[args.timestep]" in content:
        issues.append("Found old single timestep usage - should use task-specific timesteps")
    
    if issues:
        print("\n⚠️  Potential Issues:")
        for issue in issues:
            print(f"  - {issue}")
        all_passed = False
    
    # Check argument parsing
    print("\n🔍 Argument Parsing Check:")
    if "timestep_depth" in content and "timestep_normal" in content:
        print("  ✅ MTL-specific timestep arguments found")
    else:
        print("  ❌ Missing MTL timestep arguments")
        all_passed = False
    
    if 'default="mtl"' in content:
        print("  ✅ MTL as default task found")
    else:
        print("  ℹ️  MTL not set as default (this is okay)")
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 All validations passed! infer_MTL.py looks good.")
        print("\n📝 Next steps:")
        print("1. Test with a real MTL model")
        print("2. Verify timestep values match your training configuration")
        print("3. Check output directory structure")
    else:
        print("❌ Some validations failed. Please review the issues above.")
    
    return all_passed

def print_usage_examples():
    """Print usage examples for the MTL inference script"""
    print("\n📖 Usage Examples:")
    print("\n1. Multi-task inference (recommended):")
    print("   python infer_MTL.py \\")
    print("       --pretrained_model_name_or_path /path/to/mtl/model \\")
    print("       --input_dir /path/to/images \\")
    print("       --output_dir /path/to/output \\")
    print("       --task_name mtl \\")
    print("       --timestep_depth 800 \\")
    print("       --timestep_normal 600 \\")
    print("       --half_precision")
    
    print("\n2. Depth-only inference:")
    print("   python infer_MTL.py \\")
    print("       --pretrained_model_name_or_path /path/to/mtl/model \\")
    print("       --input_dir /path/to/images \\")
    print("       --output_dir /path/to/output \\")
    print("       --task_name depth \\")
    print("       --timestep_depth 800")
    
    print("\n3. Normal-only inference:")
    print("   python infer_MTL.py \\")
    print("       --pretrained_model_name_or_path /path/to/mtl/model \\")
    print("       --input_dir /path/to/images \\")
    print("       --output_dir /path/to/output \\")
    print("       --task_name normal \\")
    print("       --timestep_normal 600")

if __name__ == "__main__":
    print("🔍 Validating MTL Inference Implementation")
    print("="*50)
    
    success = validate_mtl_inference()
    print_usage_examples()
    
    sys.exit(0 if success else 1)
