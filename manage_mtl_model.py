#!/usr/bin/env python3
"""
MTL Model Management Script
管理 train_lotus_g-MTL.py 训练产生的模型文件

功能:
1. 分析模型目录结构和文件大小
2. 清理不必要的检查点文件
3. 验证推理所需文件的完整性
4. 创建轻量级推理模型包
"""

import os
import shutil
import argparse
from pathlib import Path
import json

def get_file_size(filepath):
    """获取文件大小（MB）"""
    if os.path.exists(filepath):
        return os.path.getsize(filepath) / (1024 * 1024)
    return 0

def analyze_model_directory(model_path):
    """分析模型目录结构"""
    model_path = Path(model_path)
    
    print(f"🔍 分析模型目录: {model_path}")
    print("=" * 60)
    
    # 检查必要的推理文件
    inference_files = {
        "unet/diffusion_pytorch_model.bin": "UNet 模型权重",
        "unet/config.json": "UNet 配置",
        "vae/diffusion_pytorch_model.bin": "VAE 权重", 
        "vae/config.json": "VAE 配置",
        "scheduler/scheduler_config.json": "调度器配置",
        "model_index.json": "模型索引",
        "empty_text_embeddings.pt": "空文本嵌入"
    }
    
    print("📋 推理必需文件:")
    total_inference_size = 0
    missing_files = []
    
    for file_path, description in inference_files.items():
        full_path = model_path / file_path
        size = get_file_size(full_path)
        if size > 0:
            print(f"  ✅ {file_path:<35} {size:>8.1f} MB - {description}")
            total_inference_size += size
        else:
            print(f"  ❌ {file_path:<35} {'缺失':>8} - {description}")
            missing_files.append(file_path)
    
    print(f"\n📊 推理文件总大小: {total_inference_size:.1f} MB")
    
    # 检查检查点文件
    checkpoint_dirs = [d for d in model_path.iterdir() if d.is_dir() and d.name.startswith("checkpoint-")]
    checkpoint_dirs.sort(key=lambda x: int(x.name.split("-")[1]))
    
    if checkpoint_dirs:
        print(f"\n🗂️  检查点目录 ({len(checkpoint_dirs)} 个):")
        total_checkpoint_size = 0
        
        for checkpoint_dir in checkpoint_dirs:
            checkpoint_files = {
                "optimizer.bin": "优化器状态",
                "scheduler.bin": "调度器状态", 
                "random_states_0.pkl": "随机数状态",
                "scaler.pt": "混合精度缩放器",
                "unet/diffusion_pytorch_model.bin": "UNet权重"
            }
            
            dir_size = 0
            for file_name in checkpoint_files.keys():
                file_path = checkpoint_dir / file_name
                size = get_file_size(file_path)
                dir_size += size
            
            total_checkpoint_size += dir_size
            print(f"  📁 {checkpoint_dir.name:<20} {dir_size:>8.1f} MB")
        
        print(f"\n📊 检查点文件总大小: {total_checkpoint_size:.1f} MB")
        print(f"💾 总磁盘使用: {total_inference_size + total_checkpoint_size:.1f} MB")
        
        # 计算可节省的空间
        if total_checkpoint_size > 0:
            print(f"💡 删除检查点可节省: {total_checkpoint_size:.1f} MB ({total_checkpoint_size/(total_inference_size + total_checkpoint_size)*100:.1f}%)")
    
    return missing_files, checkpoint_dirs, total_inference_size, total_checkpoint_size if checkpoint_dirs else 0

def clean_checkpoints(model_path, keep_latest=True):
    """清理检查点文件"""
    model_path = Path(model_path)
    checkpoint_dirs = [d for d in model_path.iterdir() if d.is_dir() and d.name.startswith("checkpoint-")]
    
    if not checkpoint_dirs:
        print("❌ 没有找到检查点目录")
        return
    
    checkpoint_dirs.sort(key=lambda x: int(x.name.split("-")[1]))
    
    if keep_latest and len(checkpoint_dirs) > 1:
        # 保留最新的检查点
        latest_checkpoint = checkpoint_dirs[-1]
        to_remove = checkpoint_dirs[:-1]
        print(f"🔄 保留最新检查点: {latest_checkpoint.name}")
    else:
        # 删除所有检查点
        to_remove = checkpoint_dirs
        print("🗑️  删除所有检查点")
    
    total_removed_size = 0
    for checkpoint_dir in to_remove:
        # 计算目录大小
        dir_size = sum(get_file_size(f) for f in checkpoint_dir.rglob("*") if f.is_file())
        total_removed_size += dir_size
        
        print(f"  🗑️  删除 {checkpoint_dir.name} ({dir_size:.1f} MB)")
        shutil.rmtree(checkpoint_dir)
    
    print(f"✅ 清理完成，节省空间: {total_removed_size:.1f} MB")

def create_inference_package(model_path, output_path):
    """创建轻量级推理模型包"""
    model_path = Path(model_path)
    output_path = Path(output_path)
    
    # 推理必需的文件和目录
    inference_items = [
        "unet/",
        "vae/", 
        "scheduler/",
        "model_index.json",
        "empty_text_embeddings.pt"
    ]
    
    print(f"📦 创建推理模型包: {output_path}")
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    total_size = 0
    for item in inference_items:
        src = model_path / item
        dst = output_path / item
        
        if src.exists():
            if src.is_dir():
                shutil.copytree(src, dst, dirs_exist_ok=True)
                size = sum(get_file_size(f) for f in src.rglob("*") if f.is_file())
            else:
                shutil.copy2(src, dst)
                size = get_file_size(src)
            
            total_size += size
            print(f"  ✅ 复制 {item} ({size:.1f} MB)")
        else:
            print(f"  ⚠️  跳过 {item} (不存在)")
    
    print(f"✅ 推理包创建完成，总大小: {total_size:.1f} MB")

def validate_inference_model(model_path):
    """验证推理模型的完整性"""
    model_path = Path(model_path)
    
    print(f"🔍 验证推理模型: {model_path}")
    
    # 检查必需文件
    required_files = [
        "unet/diffusion_pytorch_model.bin",
        "unet/config.json", 
        "vae/diffusion_pytorch_model.bin",
        "vae/config.json",
        "scheduler/scheduler_config.json",
        "model_index.json"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not (model_path / file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 推理模型不完整，缺少文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    # 检查配置文件
    try:
        with open(model_path / "model_index.json", 'r') as f:
            model_index = json.load(f)
        
        with open(model_path / "unet/config.json", 'r') as f:
            unet_config = json.load(f)
        
        # 验证 UNet 配置
        if unet_config.get("in_channels") != 8:
            print("⚠️  警告: UNet in_channels 不是 8，可能不是 MTL 模型")
        
        print("✅ 推理模型验证通过")
        print(f"  - UNet 输入通道: {unet_config.get('in_channels', 'unknown')}")
        print(f"  - 模型类型: {model_index.get('_class_name', 'unknown')}")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件验证失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="MTL 模型管理工具")
    parser.add_argument("model_path", help="模型目录路径")
    parser.add_argument("--analyze", action="store_true", help="分析模型目录")
    parser.add_argument("--clean", action="store_true", help="清理检查点文件")
    parser.add_argument("--keep-latest", action="store_true", help="清理时保留最新检查点")
    parser.add_argument("--create-package", help="创建推理模型包到指定路径")
    parser.add_argument("--validate", action="store_true", help="验证推理模型完整性")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model_path):
        print(f"❌ 模型路径不存在: {args.model_path}")
        return
    
    if args.analyze or (not any([args.clean, args.create_package, args.validate])):
        analyze_model_directory(args.model_path)
    
    if args.clean:
        print("\n" + "="*60)
        clean_checkpoints(args.model_path, args.keep_latest)
    
    if args.create_package:
        print("\n" + "="*60)
        create_inference_package(args.model_path, args.create_package)
    
    if args.validate:
        print("\n" + "="*60)
        validate_inference_model(args.model_path)

if __name__ == "__main__":
    main()
