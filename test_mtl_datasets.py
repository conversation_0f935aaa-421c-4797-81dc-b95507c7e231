#!/usr/bin/env python3
"""
Quick test script to verify that both Hypersim and VKITTI datasets can be loaded
and provide compatible data formats for MTL training.
"""

import torch
from utils.hypersim_dataset import get_hypersim_dataset_depth_normal
from utils.vkitti_dataset import VKIT<PERSON>Dataset, VKITTITransform, collate_fn_v<PERSON><PERSON>

def test_dataset_compatibility():
    """Test that both datasets provide compatible data formats."""
    
    print("Testing dataset compatibility for MTL training...")
    
    # Test Hypersim dataset
    try:
        print("\n1. Testing Hypersim dataset...")
        train_hypersim_dataset, preprocess_train_hypersim, collate_fn_hypersim = get_hypersim_dataset_depth_normal(
            "/home/<USER>/Lotus-main/datasets/hypersim/processed", 
            576, 
            True,  # random_flip
            norm_type="trunc_disparity", 
            truncnorm_min=0.02, 
            align_cam_normal=False
        )
        
        # Apply transform and get a sample
        if len(train_hypersim_dataset) > 0:
            train_dataset_hypersim = train_hypersim_dataset.with_transform(preprocess_train_hypersim)
            sample = train_dataset_hypersim[0]
            print(f"  Hypersim sample keys: {list(sample.keys())}")

            # Test collate function
            batch = collate_fn_hypersim([sample])
            print(f"  Hypersim batch keys: {list(batch.keys())}")
            print(f"  Hypersim batch shapes:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape}")
        else:
            print("  Hypersim dataset is empty - check dataset path")
            
    except Exception as e:
        print(f"  Hypersim dataset error: {e}")
    
    # Test VKITTI dataset
    try:
        print("\n2. Testing VKITTI dataset...")
        transform_vkitti = VKITTITransform(random_flip=True)
        train_dataset_vkitti = VKITTIDataset(
            "/home/<USER>/Lotus-main/datasets/vkitti",
            transform_vkitti,
            "trunc_disparity",
            truncnorm_min=0.02
        )
        
        # Get a sample
        if len(train_dataset_vkitti) > 0:
            sample = train_dataset_vkitti[0]
            print(f"  VKITTI sample keys: {list(sample.keys())}")
            
            # Test collate function
            batch = collate_fn_vkitti([sample])
            print(f"  VKITTI batch keys: {list(batch.keys())}")
            print(f"  VKITTI batch shapes:")
            for key, value in batch.items():
                if isinstance(value, torch.Tensor):
                    print(f"    {key}: {value.shape}")
        else:
            print("  VKITTI dataset is empty - check dataset path")
            
    except Exception as e:
        print(f"  VKITTI dataset error: {e}")
    
    print("\n3. Checking data format compatibility...")
    
    # Check if both datasets provide the required keys
    required_keys = ["pixel_values", "depth_values", "normal_values", "valid_mask_values"]
    
    print(f"  Required keys for MTL training: {required_keys}")
    print("  Both datasets should provide these keys for seamless integration.")
    
    print("\nDataset compatibility test completed!")

if __name__ == "__main__":
    test_dataset_compatibility()
