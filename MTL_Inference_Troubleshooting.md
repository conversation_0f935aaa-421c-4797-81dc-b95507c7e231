# MTL 推理故障排除指南

## 常见错误及解决方案

### 1. Pipeline 组件缺失错误

**错误信息**:
```
ValueError: Pipeline <class 'pipeline.LotusGPipeline'> expected {'image_encoder', 'feature_extractor', 'unet', 'text_encoder', 'tokenizer', 'safety_checker', 'vae', 'scheduler'}, but only {'feature_extractor', 'unet', 'tokenizer', 'vae', 'scheduler'} were passed.
```

**原因**: MTL 模型训练时移除了 `text_encoder` 等组件，但推理时 `from_pretrained` 期望所有组件都存在。

**解决方案**: ✅ **已修复** - `infer_MTL.py` 现在会自动处理缺失组件：

```python
def load_mtl_pipeline(model_path, mode, dtype, device):
    try:
        # 尝试正常加载
        pipeline = pipeline_class.from_pretrained(model_path, torch_dtype=dtype)
    except ValueError as e:
        if "expected" in str(e) and "but only" in str(e):
            # 处理 MTL 模型的缺失组件
            pipeline = pipeline_class.from_pretrained(
                model_path,
                text_encoder=None,
                tokenizer=None,
                image_encoder=None,
                safety_checker=None,
                feature_extractor=None,
                torch_dtype=dtype,
            )
```

### 2. 空文本嵌入缺失

**症状**: 推理时出现文本编码相关错误或警告

**解决方案**: ✅ **已修复** - 自动加载 `empty_text_embeddings.pt`：

```python
empty_text_embed_path = os.path.join(model_path, "empty_text_embeddings.pt")
if os.path.exists(empty_text_embed_path):
    empty_text_embeddings = torch.load(empty_text_embed_path, map_location=device)
    pipeline._cached_empty_text_embeddings = empty_text_embeddings.cpu()
```

### 3. 时间步参数错误

**症状**: 推理结果质量差或不符合预期

**可能原因**: 使用了错误的时间步值

**解决方案**: 
- 检查训练时使用的时间步值
- 默认值已更新为 `--timestep_depth 995` 和 `--timestep_normal 999`
- 根据你的训练配置调整这些值

### 4. 模型路径问题

**错误信息**: 
```
OSError: /path/to/model does not appear to be a valid git repository or a valid model identifier
```

**解决方案**:
1. 确保模型路径正确
2. 检查必需文件是否存在：
   ```bash
   python test_mtl_loading.py /path/to/your/model
   ```

### 5. GPU 内存不足

**错误信息**: 
```
RuntimeError: CUDA out of memory
```

**解决方案**:
1. 使用半精度推理：`--half_precision`
2. 降低处理分辨率：`--processing_res 512`
3. 使用 CPU 推理（较慢）

## 验证步骤

### 1. 测试模型加载
```bash
python test_mtl_loading.py /path/to/your/mtl/model --mode regression
```

### 2. 分析模型结构
```bash
python manage_mtl_model.py /path/to/your/mtl/model --analyze
```

### 3. 验证模型完整性
```bash
python manage_mtl_model.py /path/to/your/mtl/model --validate
```

## 推理测试

### 基本 MTL 推理测试
```bash
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/test/images \
    --output_dir /path/to/output \
    --task_name mtl \
    --timestep_depth 995 \
    --timestep_normal 999 \
    --half_precision
```

### 单任务测试
```bash
# 仅深度估计
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/test/images \
    --output_dir /path/to/output \
    --task_name depth \
    --timestep_depth 995

# 仅法线估计  
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/test/images \
    --output_dir /path/to/output \
    --task_name normal \
    --timestep_normal 999
```

## 性能优化建议

### 1. 硬件优化
- **GPU**: 使用 `--half_precision` 和适当的 `--processing_res`
- **CPU**: 降低分辨率，考虑使用更少的推理步骤

### 2. 批处理优化
- MTL 模式比分别运行两个单任务更高效
- 对于大量图像，考虑批处理处理

### 3. 内存管理
- 定期清理 GPU 缓存：`torch.cuda.empty_cache()`
- 使用适当的数据类型：`torch.float16` vs `torch.float32`

## 调试技巧

### 1. 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 2. 检查模型组件
```python
print("Pipeline components:")
for name, component in pipeline.components.items():
    print(f"  {name}: {type(component) if component else None}")
```

### 3. 验证输入数据
```python
print(f"Input image shape: {test_image.shape}")
print(f"Input image range: [{test_image.min():.3f}, {test_image.max():.3f}]")
```

## 常见问题 FAQ

**Q: 为什么 MTL 模型没有 text_encoder？**
A: MTL 训练时使用预计算的空文本嵌入来提高效率，不需要 text_encoder。

**Q: 如何确定正确的时间步值？**
A: 查看训练日志或使用训练时的 `--timestep_depth` 和 `--timestep_normal` 参数值。

**Q: MTL 推理比单任务推理慢吗？**
A: 对于同时需要深度和法线的情况，MTL 推理更快，因为只需要一次前向传播。

**Q: 可以在单任务模型上使用 infer_MTL.py 吗？**
A: 理论上可以，但建议使用对应的单任务推理脚本以获得最佳性能。

## 获取帮助

如果遇到其他问题：
1. 检查模型文件完整性
2. 验证输入数据格式
3. 查看详细错误日志
4. 使用提供的测试脚本进行诊断
