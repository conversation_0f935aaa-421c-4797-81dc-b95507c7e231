# Lotus MTL Inference Guide

This guide explains how to use the `infer_MTL.py` script for multi-task learning (MTL) inference with Lotus models trained using `train_lotus_g-MTL.py`.

## Overview

The MTL inference script supports:
- **Simultaneous depth and normal estimation** from a single RGB image
- **Individual task inference** (depth-only or normal-only) using the MTL model
- **Timestep-based task conditioning** (no task embeddings required)

## Key Features

### 1. Multi-Task Inference (MTL)
- Single forward pass through the model
- Simultaneous depth and normal map generation
- Efficient processing using task-specific timesteps

### 2. Task-Specific Timesteps
- **Depth estimation**: Uses `--timestep_depth` (default: 800)
- **Normal estimation**: Uses `--timestep_normal` (default: 600)
- No task embeddings required (unlike single-task models)

### 3. Flexible Output Modes
- **MTL mode**: Generates both depth and normal maps
- **Single task modes**: Depth-only or normal-only inference

## Usage

### Basic MTL Inference (Recommended)
```bash
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/input/images \
    --output_dir /path/to/output \
    --task_name mtl \
    --timestep_depth 800 \
    --timestep_normal 600 \
    --mode regression \
    --processing_res 768 \
    --half_precision
```

### Depth-Only Inference
```bash
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/input/images \
    --output_dir /path/to/output \
    --task_name depth \
    --timestep_depth 800 \
    --disparity  # Optional: for disparity instead of depth
```

### Normal-Only Inference
```bash
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --input_dir /path/to/input/images \
    --output_dir /path/to/output \
    --task_name normal \
    --timestep_normal 600
```

## Command Line Arguments

### Model Settings
- `--pretrained_model_name_or_path`: Path to the MTL model
- `--prediction_type`: Prediction type (default: "sample")
- `--mode`: Pipeline mode (default: "regression")

### Task Configuration
- `--task_name`: Task mode ("mtl", "depth", "normal")
- `--timestep_depth`: Timestep for depth estimation (default: 800)
- `--timestep_normal`: Timestep for normal estimation (default: 600)
- `--disparity`: Use disparity instead of depth (depth task only)

### Inference Settings
- `--input_dir`: Directory containing input RGB images
- `--output_dir`: Directory for saving results
- `--processing_res`: Processing resolution (default: 768)
- `--half_precision`: Use FP16 for faster inference
- `--seed`: Random seed for reproducibility

## Output Structure

### MTL Mode (`--task_name mtl`)
```
output_dir/
├── depth_vis/          # Depth visualizations (colorized)
├── depth/              # Depth numpy arrays (.npy)
├── normal_vis/         # Normal visualizations (RGB images)
└── normal/             # Normal numpy arrays (.npy)
```

### Single Task Mode
```
output_dir/
├── {task_name}_vis/    # Task visualizations
└── {task_name}/        # Task numpy arrays (.npy)
```

## Model Compatibility

This inference script is designed for MTL models trained with:
- `train_lotus_g-MTL.py` (timestep-based conditioning)
- **No task switcher** (class_labels removed)
- **Timestep as task embedding** approach

## Performance Tips

1. **Use `--half_precision`** for faster inference on modern GPUs
2. **Adjust `--processing_res`** based on your GPU memory
3. **Use MTL mode** for maximum efficiency when both outputs are needed
4. **Set appropriate timesteps** based on your training configuration

## Troubleshooting

### Common Issues
1. **Model loading errors**: Ensure the model was trained with MTL script
2. **Memory issues**: Reduce `--processing_res` or use `--half_precision`
3. **Wrong timesteps**: Use the same timesteps as during training

### Timestep Selection
- Use the same timestep values that were used during MTL training
- Default values (depth: 800, normal: 600) work well for most cases
- Check your training logs for optimal timestep values

## Example Results

The MTL inference will generate:
- **Depth maps**: Grayscale depth/disparity maps with color visualization
- **Normal maps**: RGB normal maps showing surface orientations
- **Numpy arrays**: Raw prediction data for further processing

## Integration with Evaluation

The generated outputs are compatible with Lotus evaluation scripts:
- Use depth outputs with `evaluation/depth/` evaluation tools
- Use normal outputs with `evaluation/normal/` evaluation tools
