'''
一个数据集条目csv文件的内容结构如下：
scene_name,camera_name,frame_id,included_in_public_release,exclude_reason,split_partition_name
ai_001_004,cam_00,0,True,,train
ai_001_004,cam_00,1,True,,train
ai_001_004,cam_00,2,True,,train
ai_001_004,cam_00,3,True,,train
ai_001_004,cam_00,4,True,,train
......
此代码随机挑选指定数量的行，将此行中的train修改为val或者test，并将修改后的csv文件保存
比如给定valnum=50和testnum=40，即将csv中随机挑选50行修改为val，随机挑选40行修改为test
'''

import pandas as pd
import random
import argparse

def parse_args():
    parser = argparse.ArgumentParser(description='Randomly resplit dataset into train/val/test')
    parser.add_argument('--csv_path', type=str, required=True, help='Path to the input CSV file')
    parser.add_argument('--output_path', type=str, required=True, help='Path to save the output CSV file')
    parser.add_argument('--val_num', type=int, required=True, help='Number of samples for validation set')
    parser.add_argument('--test_num', type=int, required=True, help='Number of samples for test set')
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    return parser.parse_args()

def resplit_dataset(csv_path, output_path, val_num, test_num, seed=42):
    # Set random seed for reproducibility
    random.seed(seed)
    
    # Read the CSV file
    df = pd.read_csv(csv_path)
    
    # Get indices of rows where split_partition_name is 'train'
    train_indices = df[df['split_partition_name'] == 'train'].index.tolist()
    
    if len(train_indices) < (val_num + test_num):
        raise ValueError(f"Not enough training samples. Available: {len(train_indices)}, Requested: {val_num + test_num}")
    
    # Randomly select indices for validation and test sets
    val_indices = random.sample(train_indices, val_num)
    remaining_indices = list(set(train_indices) - set(val_indices))
    test_indices = random.sample(remaining_indices, test_num)
    
    # Update split_partition_name
    df.loc[val_indices, 'split_partition_name'] = 'val'
    df.loc[test_indices, 'split_partition_name'] = 'test'
    
    # Save the modified DataFrame
    df.to_csv(output_path, index=False)
    
    # Print statistics
    print(f"Dataset split statistics:")
    print(df['split_partition_name'].value_counts())

def main():
    args = parse_args()
    resplit_dataset(
        csv_path=args.csv_path,
        output_path=args.output_path,
        val_num=args.val_num,
        test_num=args.test_num,
        seed=args.seed
    )

if __name__ == "__main__":
    main()
