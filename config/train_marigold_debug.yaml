base_config:
- config/train_marigold.yaml

dataloader:
  num_workers: 4
  effective_batch_size: 8
  max_train_batch_size: 1
  seed: 2025  # to ensure continuity when resuming from checkpoint

# Override depth normalization to use trunc_disparity
depth_normalization:
  type: trunc_disparity
  clip: true
  norm_min: -1.0
  norm_max: 1.0
  min_max_quantile: 0.02

# Disabled multi-resolution noise, using standard noise instead
multi_res_noise: null

# Validation settings for disparity
validation:
  denoising_steps: 1  # Single-step inference like Lotus
  fixed_timestep: 999  # Use the same fixed timestep for inference
  ensemble_size: 1  # simplified setting for on-training validation
  processing_res: 0
  match_input_res: false
  resample_method: bilinear
  main_val_metric: abs_relative_difference
  main_val_metric_goal: minimize
  init_seed: 2024

# Training settings
trainer:
  save_period: 5
  backup_period: 10
  validation_period: 5
  visualization_period: 5

max_epoch: 10  # a debug enough number
max_iter: 50  # usually converges at around 20k

eval:
  alignment: least_square_disparity  # Use disparity-based alignment
  align_max_res: null
  eval_metrics:
  - abs_relative_difference
  - squared_relative_difference
  - rmse_linear
  - rmse_log
  - log10
  - delta1_acc
  - delta2_acc
  - delta3_acc
  - i_rmse
  - silog_rmse
