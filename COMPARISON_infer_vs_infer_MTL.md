# Comparison: infer.py vs infer_MTL.py

This document compares the original single-task inference script (`infer.py`) with the new multi-task learning inference script (`infer_MTL.py`).

## Key Differences

### 1. Task Conditioning Approach

**infer.py (Single-task)**:
```python
# Uses task embeddings
task_emb = torch.tensor([1, 0]).float().unsqueeze(0).repeat(1, 1).to(device)
task_emb = torch.cat([torch.sin(task_emb), torch.cos(task_emb)], dim=-1).repeat(1, 1)

pred = pipeline(
    rgb_in=test_image,
    timesteps=[args.timestep],  # Fixed timestep (999)
    task_emb=task_emb,          # Task embedding required
    ...
)
```

**infer_MTL.py (Multi-task)**:
```python
# Uses timestep-based conditioning (no task embeddings)
pred_depth = pipeline(
    rgb_in=test_image,
    timesteps=[args.timestep_depth],  # Task-specific timestep (800)
    # No task_emb parameter needed
    ...
)

pred_normal = pipeline(
    rgb_in=test_image,
    timesteps=[args.timestep_normal],  # Task-specific timestep (600)
    # No task_emb parameter needed
    ...
)
```

### 2. Model Architecture Compatibility

| Feature | infer.py | infer_MTL.py |
|---------|----------|--------------|
| Task Switcher | Required (class_labels) | Not used (removed in MTL) |
| Task Embeddings | Required | Not used |
| Timestep Strategy | Fixed (999) | Task-specific (800, 600) |
| Model Type | Single-task trained | MTL trained |

### 3. Inference Capabilities

**infer.py**:
- Single task per run (depth OR normal)
- Requires separate runs for multiple tasks
- Uses task embeddings for conditioning

**infer_MTL.py**:
- Multi-task in single run (depth AND normal)
- Can also run single tasks efficiently
- Uses timesteps for task conditioning

### 4. Command Line Arguments

**New in infer_MTL.py**:
```bash
--timestep_depth 800      # Depth task timestep
--timestep_normal 600     # Normal task timestep
--task_name mtl           # Multi-task mode
```

**Removed from infer_MTL.py**:
```bash
--timestep 999            # Single fixed timestep
# task_emb is handled internally
```

### 5. Output Structure

**infer.py**:
```
output_dir/
├── depth_vis/     # OR normal_vis/
└── depth/         # OR normal/
```

**infer_MTL.py (MTL mode)**:
```
output_dir/
├── depth_vis/     # Depth visualizations
├── depth/         # Depth arrays
├── normal_vis/    # Normal visualizations
└── normal/        # Normal arrays
```

## Usage Examples

### Original Single-Task Inference
```bash
# Depth estimation
python infer.py \
    --pretrained_model_name_or_path /path/to/single/task/model \
    --task_name depth \
    --timestep 999 \
    --input_dir /path/to/images \
    --output_dir /path/to/output

# Normal estimation (separate run required)
python infer.py \
    --pretrained_model_name_or_path /path/to/single/task/model \
    --task_name normal \
    --timestep 999 \
    --input_dir /path/to/images \
    --output_dir /path/to/output
```

### New MTL Inference
```bash
# Both depth and normal in single run
python infer_MTL.py \
    --pretrained_model_name_or_path /path/to/mtl/model \
    --task_name mtl \
    --timestep_depth 800 \
    --timestep_normal 600 \
    --input_dir /path/to/images \
    --output_dir /path/to/output
```

## Performance Comparison

| Aspect | infer.py | infer_MTL.py |
|--------|----------|--------------|
| **Speed (both tasks)** | 2× inference time | 1× inference time |
| **Memory Usage** | Lower per run | Higher per run |
| **GPU Utilization** | 2 separate runs | 1 combined run |
| **I/O Operations** | 2× image loading | 1× image loading |

## Migration Guide

### From Single-Task to MTL Inference

1. **Update model path**: Use MTL-trained model
2. **Change script**: Use `infer_MTL.py` instead of `infer.py`
3. **Update arguments**:
   - Replace `--timestep 999` with `--timestep_depth 800 --timestep_normal 600`
   - Change `--task_name depth` to `--task_name mtl`
4. **Update output handling**: Account for both depth and normal outputs

### Backward Compatibility

The MTL inference script maintains compatibility for single-task inference:
```bash
# Still works for single tasks
python infer_MTL.py --task_name depth --timestep_depth 800 ...
python infer_MTL.py --task_name normal --timestep_normal 600 ...
```

## When to Use Which Script

**Use infer.py when**:
- Working with single-task trained models
- Only need one type of output (depth OR normal)
- Using models trained with task embeddings

**Use infer_MTL.py when**:
- Working with MTL-trained models
- Need both depth and normal outputs
- Want maximum efficiency for multi-task inference
- Using models trained with timestep-based conditioning
