# MTL 模型组件分析：train_lotus_g-MTL.py 保存的文件结构

## 概述

`train_lotus_g-MTL.py` 训练脚本在 `output_dir/train_name` 路径下保存了多种类型的文件，主要分为两类：
1. **训练检查点** (Checkpoints) - 用于恢复训练
2. **最终模型** (Final Model) - 用于推理

## 详细文件结构分析

### 📁 完整目录结构
```
output_dir/train_name/
├── checkpoint-11500/           # 训练检查点目录
│   ├── optimizer.bin          # 优化器状态
│   ├── scheduler.bin          # 学习率调度器状态
│   ├── random_states_0.pkl    # 随机数生成器状态
│   ├── scaler.pt             # 混合精度缩放器状态
│   └── unet/                 # UNet模型权重
│       ├── config.json       # UNet配置
│       └── diffusion_pytorch_model.bin  # UNet权重
├── unet/                      # 最终训练好的UNet模型
│   ├── config.json           # UNet配置文件
│   └── diffusion_pytorch_model.bin  # UNet权重文件
├── scheduler/                 # 噪声调度器
│   └── scheduler_config.json # 调度器配置
├── vae/                      # VAE编码器/解码器
│   ├── config.json          # VAE配置
│   └── diffusion_pytorch_model.bin  # VAE权重
├── empty_text_embeddings.pt  # 空文本嵌入
├── model_index.json         # 模型索引文件
├── images/                  # 验证图像输出
├── evaluation-xxxxx/        # 评估结果
└── logs/                    # TensorBoard日志
```

## 🔍 关键组件详解

### 1. checkpoint-11500/ 目录（训练检查点）

#### `optimizer.bin`
- **作用**: 保存 AdamW 优化器的状态
- **内容**: 
  - 参数的一阶矩估计 (momentum)
  - 参数的二阶矩估计 (variance)
  - 步数计数器
- **大小**: 通常很大（几GB），因为包含所有可训练参数的优化器状态
- **用途**: 恢复训练时保持优化器的历史信息

#### `scheduler.bin`
- **作用**: 保存学习率调度器状态
- **内容**: 当前学习率、步数、调度器参数
- **用途**: 恢复训练时保持正确的学习率

#### `random_states_0.pkl`
- **作用**: 保存随机数生成器状态
- **内容**: PyTorch、NumPy、Python 随机数生成器的状态
- **用途**: 确保训练恢复后的随机性一致

#### `scaler.pt`
- **作用**: 混合精度训练的缩放器状态
- **内容**: 损失缩放因子、缩放历史
- **用途**: 恢复混合精度训练状态

#### `checkpoint-11500/unet/`
- **作用**: 检查点时刻的 UNet 模型权重
- **与根目录 unet/ 的关系**: 
  - `checkpoint-11500/unet/` 是第11500步的模型状态
  - 根目录 `unet/` 是训练完成后的最终模型状态

### 2. 根目录下的模型组件

#### `unet/` 目录（最终模型）
```python
# 来自 save_model_hook 函数
def save_model_hook(models, weights, output_dir):
    if accelerator.is_main_process:
        for i, model in enumerate(models):
            model.save_pretrained(os.path.join(output_dir, "unet"))
```
- **作用**: 最终训练好的 UNet 模型
- **特点**: 
  - 输入通道数为 8 (RGB + 深度/法线)
  - 支持 MTL 多任务学习
  - 移除了 task switcher (class_labels)

#### `scheduler/` 和 `vae/`
```python
# 来自 pipeline.save_pretrained()
pipeline = LotusGPipeline.from_pretrained(
    args.pretrained_model_name_or_path,
    text_encoder=None,  # 无文本编码器
    vae=vae,
    unet=unet,
    revision=args.revision,
    variant=args.variant,
)
pipeline.save_pretrained(args.output_dir)
```
- **scheduler/**: DDIM 噪声调度器配置
- **vae/**: 从预训练模型复制的 VAE 组件

#### `empty_text_embeddings.pt`
```python
# 保存空文本嵌入
embeddings_output_path = os.path.join(args.output_dir, "empty_text_embeddings.pt")
torch.save(empty_text_embeddings.cpu(), embeddings_output_path)
```
- **作用**: 预计算的空文本嵌入
- **用途**: 推理时避免重复计算，提高效率

## 🎯 推理时使用哪些文件

### 对于 `infer_MTL.py`：
```python
# 主要使用根目录下的组件
pipeline = LotusGPipeline.from_pretrained(
    args.pretrained_model_name_or_path,  # 指向 output_dir/train_name
    torch_dtype=dtype,
)
```

**实际加载的文件**:
- `unet/diffusion_pytorch_model.bin` - UNet 权重
- `vae/diffusion_pytorch_model.bin` - VAE 权重  
- `scheduler/scheduler_config.json` - 调度器配置
- `model_index.json` - 模型索引
- `empty_text_embeddings.pt` - 空文本嵌入（可选）

**不需要的文件**:
- `checkpoint-*/` 目录下的所有文件（仅用于恢复训练）
- `optimizer.bin`, `scheduler.bin` 等训练状态文件

## 💡 重要区别

### Checkpoint UNet vs 根目录 UNet
| 特征 | checkpoint-11500/unet/ | 根目录 unet/ |
|------|----------------------|-------------|
| **用途** | 恢复训练 | 推理使用 |
| **时间点** | 第11500步 | 训练完成 |
| **完整性** | 训练中间状态 | 最终优化状态 |
| **推荐使用** | 继续训练 | 生产推理 |

### 文件大小参考
- `optimizer.bin`: ~2-4GB (最大)
- `unet/diffusion_pytorch_model.bin`: ~1-2GB
- `vae/diffusion_pytorch_model.bin`: ~300-500MB
- `empty_text_embeddings.pt`: ~1-2MB
- 其他配置文件: <1MB

## 🚀 使用建议

1. **推理使用**: 直接指向 `output_dir/train_name` 作为模型路径
2. **继续训练**: 使用 `--resume_from_checkpoint checkpoint-11500`
3. **存储优化**: 可以删除 `checkpoint-*/` 目录以节省空间（如果不需要恢复训练）
4. **模型分发**: 只需要根目录下的模型文件，不需要检查点文件
