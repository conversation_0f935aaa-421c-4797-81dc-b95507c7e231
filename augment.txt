Now the `train_lotus_g-MTL.py` has eliminated the default task_emb mechanism in the original `train_lotus_g.py` and repurposed the timestep for multi-task conditioning to switch task training.

`train_lotus_d-MTL_dev.py` now is a copy of `train_lotus_g-MTL.py`. Please modify the script `train_lotus_d-MTL_dev.py`  to a new implementation that adapts the multi-task learning (MTL) approach from `train_lotus_g-MTL.py` to the deterministic Lotus-D paradigm.  

The key differences between Lotus-G (generative) and Lotus-D (deterministic) paradigms:
1. In Lotus-G: RGB latents are combined with noisy target latents as UNet input
2. In Lotus-D: RGB latents are used directly without adding noise

Based on the `train_lotus_d.py` file, modify the `train_lotus_d-MTL_dev.py` implementation to:
1. Remove the noise sampling and addition steps from the training process
2. Maintain all core MTL features including: 
- Dynamic timestep assignment for different tasks (RGB, depth, normal), do not restore the task_emb mechanism!
- Task-specific loss weighting
- Task frequency control via ratios
- Multi-dataset handling (Hypersim and VKITTI)
- Evaluation procedures for all tasks
 
Ensure the pipeline structure follows the deterministic approach while preserving the MTL task switching mechanism that uses different timesteps instead of task embeddings.
