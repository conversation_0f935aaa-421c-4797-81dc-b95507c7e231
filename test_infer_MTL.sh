#!/bin/bash

# Test script for MTL inference
# This script demonstrates how to use the infer_MTL.py for different inference modes

# Set common parameters
MODEL_PATH="path/to/your/mtl/model"  # Replace with your actual MTL model path
INPUT_DIR="path/to/input/images"     # Replace with your input directory
OUTPUT_DIR="outputs/mtl_inference"   # Output directory

# Create output directory
mkdir -p $OUTPUT_DIR

echo "=== Lotus MTL Inference Test Script ==="
echo ""

# 1. Multi-task inference (both depth and normal simultaneously)
echo "1. Running MTL inference (depth + normal)..."
python infer_MTL.py \
    --pretrained_model_name_or_path $MODEL_PATH \
    --input_dir $INPUT_DIR \
    --output_dir $OUTPUT_DIR/mtl \
    --task_name mtl \
    --timestep_depth 800 \
    --timestep_normal 600 \
    --mode regression \
    --processing_res 768 \
    --half_precision

echo ""

# 2. Depth-only inference using MTL model
echo "2. Running depth-only inference with MTL model..."
python infer_MTL.py \
    --pretrained_model_name_or_path $MODEL_PATH \
    --input_dir $INPUT_DIR \
    --output_dir $OUTPUT_DIR/depth_only \
    --task_name depth \
    --timestep_depth 800 \
    --mode regression \
    --processing_res 768 \
    --disparity \
    --half_precision

echo ""

# 3. Normal-only inference using MTL model
echo "3. Running normal-only inference with MTL model..."
python infer_MTL.py \
    --pretrained_model_name_or_path $MODEL_PATH \
    --input_dir $INPUT_DIR \
    --output_dir $OUTPUT_DIR/normal_only \
    --task_name normal \
    --timestep_normal 600 \
    --mode regression \
    --processing_res 768 \
    --half_precision

echo ""
echo "=== All inference tests completed! ==="
echo "Check the following directories for results:"
echo "- MTL (both tasks): $OUTPUT_DIR/mtl/"
echo "- Depth only: $OUTPUT_DIR/depth_only/"
echo "- Normal only: $OUTPUT_DIR/normal_only/"
